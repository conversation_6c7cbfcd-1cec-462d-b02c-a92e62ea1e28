1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.sidimohamed.modetaris"
4    android:versionCode="21"
5    android:versionName="2.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:5:6-68
11-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:5:23-65
12    <!-- Add storage permissions -->
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:7:6-82
13-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:7:23-79
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:8:6-81
14-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:8:23-78
15
16    <!-- Declare packages the app needs to query -->
17    <queries>
17-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:12:5-14:15
18        <package android:name="com.mojang.minecraftpe" />
18-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:13:9-58
18-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:13:18-55
19        <!-- For browser content -->
20        <intent>
20-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:38:9-44:18
21            <action android:name="android.intent.action.VIEW" />
21-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:39:13-65
21-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:39:21-62
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:41:13-74
23-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:41:23-71
24
25            <data android:scheme="https" />
25-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:43:13-44
25-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:43:19-41
26        </intent> <!-- End of browser content -->
27        <!-- For CustomTabsService -->
28        <intent>
28-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:47:9-49:18
29            <action android:name="android.support.customtabs.action.CustomTabsService" />
29-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:48:13-90
29-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:48:21-87
30        </intent>
31    </queries>
32
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:25:5-79
33-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:25:22-76
34    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
34-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:26:5-79
34-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:26:22-76
35    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
35-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:27:5-82
35-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:27:22-79
36    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
36-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:28:5-88
36-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:28:22-85
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
37-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:29:5-83
37-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:29:22-80
38    <uses-permission android:name="android.permission.WAKE_LOCK" />
38-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
38-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
39-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
39-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
40
41    <permission
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
42        android:name="com.sidimohamed.modetaris.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
43        android:protectionLevel="signature" />
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
44
45    <uses-permission android:name="com.sidimohamed.modetaris.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
46
47    <application
47-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:16:5-84:19
48        android:allowBackup="true"
48-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:17:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/data_extraction_rules"
50-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:18:9-65
51        android:debuggable="true"
52        android:extractNativeLibs="false"
53        android:fullBackupContent="@xml/backup_rules"
53-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:19:9-54
54        android:hardwareAccelerated="true"
54-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:26:9-43
55        android:icon="@mipmap/ic_launcher"
55-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:20:9-43
56        android:label="@string/app_name"
56-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:21:9-41
57        android:largeHeap="true"
57-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:27:9-33
58        android:requestLegacyExternalStorage="true"
58-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:25:9-52
59        android:roundIcon="@mipmap/ic_launcher_round"
59-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:22:9-54
60        android:supportsRtl="true"
60-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:23:9-35
61        android:theme="@style/Theme.Modetaris" >
61-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:24:9-47
62
63        <!-- AdMob Activity Configuration -->
64        <activity
64-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:31:9-35:66
65            android:name="com.google.android.gms.ads.AdActivity"
65-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:32:13-65
66            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
66-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:33:13-122
67            android:exported="false"
67-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:59:13-37
68            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Added android:theme to replace -->
68-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:34:13-72
69
70
71        <!-- Sample AdMob App ID. Replace with your actual App ID from AdMob UI. -->
72        <meta-data
72-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:38:9-40:69
73            android:name="com.google.android.gms.ads.APPLICATION_ID"
73-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:39:13-69
74            android:value="ca-app-pub-4373910379376809~4188966917" />
74-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:40:13-67
75
76        <!-- Removed SplashActivity declaration -->
77
78
79        <!-- Main Activity (Restored as launcher, apply splash theme) -->
80        <activity
80-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:45:9-55:20
81            android:name="com.sidimohamed.modetaris.MainActivity"
81-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:46:13-41
82            android:exported="true"
82-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:47:13-36
83            android:label="@string/app_name"
83-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:48:13-45
84            android:theme="@style/Theme.App.Starting" > <!-- Apply splash theme -->
84-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:49:13-54
85            <intent-filter>
85-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:50:13-54:29
86                <action android:name="android.intent.action.MAIN" />
86-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:51:17-69
86-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:51:25-66
87
88                <category android:name="android.intent.category.LAUNCHER" />
88-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:53:17-77
88-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:53:27-74
89            </intent-filter>
90        </activity>
91
92        <!-- Define FileProvider -->
93        <provider
94            android:name="androidx.core.content.FileProvider"
94-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:59:13-62
95            android:authorities="com.sidimohamed.modetaris.provider"
95-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:60:13-60
96            android:exported="false"
96-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:61:13-37
97            android:grantUriPermissions="true" >
97-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:62:13-47
98            <meta-data
98-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:63:13-65:58
99                android:name="android.support.FILE_PROVIDER_PATHS"
99-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:64:17-67
100                android:resource="@xml/provider_paths" />
100-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:65:17-55
101        </provider>
102
103        <!-- Define the DownloadReceiver -->
104        <receiver
104-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:69:9-75:20
105            android:name="com.sidimohamed.modetaris.DownloadReceiver"
105-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:70:13-45
106            android:exported="false" >
106-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:71:13-37
107            <intent-filter>
107-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:72:13-74:29
108                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
108-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:73:17-81
108-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:73:25-79
109            </intent-filter>
110        </receiver>
111
112        <!-- Placeholder for DropDataProvider to resolve ANR -->
113        <provider
114            android:name="com.sidimohamed.modetaris.DropDataProvider"
114-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:79:13-45
115            android:authorities="com.sidimohamed.modetaris.DropDataProvider"
115-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:80:13-68
116            android:exported="false"
116-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:81:13-37
117            android:grantUriPermissions="true" />
117-->C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:82:13-47
118
119        <activity
119-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
120            android:name="androidx.compose.ui.tooling.PreviewActivity"
120-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
121            android:exported="true" />
121-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
122        <activity
122-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
123            android:name="androidx.activity.ComponentActivity"
123-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
124            android:exported="true" />
124-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
125
126        <provider
126-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:63:9-68:43
127            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
127-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:64:13-76
128            android:authorities="com.sidimohamed.modetaris.mobileadsinitprovider"
128-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:65:13-73
129            android:exported="false"
129-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:66:13-37
130            android:initOrder="100" />
130-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:67:13-36
131
132        <service
132-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:70:9-74:43
133            android:name="com.google.android.gms.ads.AdService"
133-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:71:13-64
134            android:enabled="true"
134-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:72:13-35
135            android:exported="false" />
135-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:73:13-37
136
137        <activity
137-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:76:9-80:43
138            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
138-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:77:13-82
139            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
139-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:78:13-122
140            android:exported="false" />
140-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:79:13-37
141        <activity
141-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:81:9-88:43
142            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
142-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:82:13-82
143            android:excludeFromRecents="true"
143-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:83:13-46
144            android:exported="false"
144-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:84:13-37
145            android:launchMode="singleTask"
145-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:85:13-44
146            android:taskAffinity=""
146-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:86:13-36
147            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
147-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:87:13-72
148
149        <property
149-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:90:9-92:62
150            android:name="android.adservices.AD_SERVICES_CONFIG"
150-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:91:13-65
151            android:resource="@xml/gma_ad_services_config" />
151-->[com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:92:13-59
152
153        <activity
153-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
154            android:name="com.google.android.gms.common.api.GoogleApiActivity"
154-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:19-85
155            android:exported="false"
155-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:22:19-43
156            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
156-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:21:19-78
157
158        <meta-data
158-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
159            android:name="com.google.android.gms.version"
159-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
160            android:value="@integer/google_play_services_version" />
160-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
161
162        <provider
162-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
163            android:name="androidx.startup.InitializationProvider"
163-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
164            android:authorities="com.sidimohamed.modetaris.androidx-startup"
164-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
165            android:exported="false" >
165-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
166            <meta-data
166-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
167                android:name="androidx.work.WorkManagerInitializer"
167-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
168                android:value="androidx.startup" />
168-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
169            <meta-data
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
170                android:name="androidx.emoji2.text.EmojiCompatInitializer"
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
171                android:value="androidx.startup" />
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
172            <meta-data
172-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
173                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
173-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
174                android:value="androidx.startup" />
174-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
175            <meta-data
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
176                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
176-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
177                android:value="androidx.startup" />
177-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
178        </provider>
179
180        <service
180-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
181            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
181-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
182            android:directBootAware="false"
182-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
183            android:enabled="@bool/enable_system_alarm_service_default"
183-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
184            android:exported="false" />
184-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
185        <service
185-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
186            android:name="androidx.work.impl.background.systemjob.SystemJobService"
186-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
187            android:directBootAware="false"
187-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
188            android:enabled="@bool/enable_system_job_service_default"
188-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
189            android:exported="true"
189-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
190            android:permission="android.permission.BIND_JOB_SERVICE" />
190-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
191        <service
191-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
192            android:name="androidx.work.impl.foreground.SystemForegroundService"
192-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
193            android:directBootAware="false"
193-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
194            android:enabled="@bool/enable_system_foreground_service_default"
194-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
195            android:exported="false" />
195-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
196
197        <receiver
197-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
198            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
198-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
199            android:directBootAware="false"
199-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
200            android:enabled="true"
200-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
201            android:exported="false" />
201-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
202        <receiver
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
203            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
204            android:directBootAware="false"
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
205            android:enabled="false"
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
206            android:exported="false" >
206-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
207            <intent-filter>
207-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
208                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
208-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
208-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
209                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
209-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
209-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
210            </intent-filter>
211        </receiver>
212        <receiver
212-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
213            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
213-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
214            android:directBootAware="false"
214-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
215            android:enabled="false"
215-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
216            android:exported="false" >
216-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
217            <intent-filter>
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
218                <action android:name="android.intent.action.BATTERY_OKAY" />
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
219                <action android:name="android.intent.action.BATTERY_LOW" />
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
220            </intent-filter>
221        </receiver>
222        <receiver
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
223            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
224            android:directBootAware="false"
224-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
225            android:enabled="false"
225-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
226            android:exported="false" >
226-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
227            <intent-filter>
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
228                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
229                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
230            </intent-filter>
231        </receiver>
232        <receiver
232-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
233            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
233-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
234            android:directBootAware="false"
234-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
235            android:enabled="false"
235-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
236            android:exported="false" >
236-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
237            <intent-filter>
237-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
238                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
238-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
238-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
239            </intent-filter>
240        </receiver>
241        <receiver
241-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
242            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
242-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
243            android:directBootAware="false"
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
244            android:enabled="false"
244-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
245            android:exported="false" >
245-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
246            <intent-filter>
246-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
247                <action android:name="android.intent.action.BOOT_COMPLETED" />
247-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
247-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
248                <action android:name="android.intent.action.TIME_SET" />
248-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
248-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
249                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
249-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
249-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
250            </intent-filter>
251        </receiver>
252        <receiver
252-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
253            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
253-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
254            android:directBootAware="false"
254-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
255            android:enabled="@bool/enable_system_alarm_service_default"
255-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
256            android:exported="false" >
256-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
257            <intent-filter>
257-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
258                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
258-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
258-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
259            </intent-filter>
260        </receiver>
261        <receiver
261-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
262            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
262-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
263            android:directBootAware="false"
263-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
264            android:enabled="true"
264-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
265            android:exported="true"
265-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
266            android:permission="android.permission.DUMP" >
266-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
267            <intent-filter>
267-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
268                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
268-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
268-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
269            </intent-filter>
270        </receiver>
271
272        <uses-library
272-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
273            android:name="android.ext.adservices"
273-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
274            android:required="false" />
274-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
275
276        <receiver
276-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
277            android:name="androidx.profileinstaller.ProfileInstallReceiver"
277-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
278            android:directBootAware="false"
278-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
279            android:enabled="true"
279-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
280            android:exported="true"
280-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
281            android:permission="android.permission.DUMP" >
281-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
282            <intent-filter>
282-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
283                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
283-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
283-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
284            </intent-filter>
285            <intent-filter>
285-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
286                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
286-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
286-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
287            </intent-filter>
288            <intent-filter>
288-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
289                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
289-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
289-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
290            </intent-filter>
291            <intent-filter>
291-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
292                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
292-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
292-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
293            </intent-filter>
294        </receiver>
295
296        <service
296-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
297            android:name="androidx.room.MultiInstanceInvalidationService"
297-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
298            android:directBootAware="true"
298-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
299            android:exported="false" />
299-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
300    </application>
301
302</manifest>
