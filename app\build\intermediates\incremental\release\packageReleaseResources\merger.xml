<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\modetaris\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\modetaris\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\modetaris\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\modetaris\app\src\main\res\drawable\ic_launcher_foreground.jpg" qualifiers="" type="drawable"/><file name="ic_notification" path="C:\Users\<USER>\modetaris\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="loading_photo" path="C:\Users\<USER>\modetaris\app\src\main\res\drawable\loading_photo.jpg" qualifiers="" type="drawable"/><file name="splash_background" path="C:\Users\<USER>\modetaris\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\modetaris\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_new" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-hdpi\ic_launcher_new.jpg" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_new" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-mdpi\ic_launcher_new.jpg" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_new" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-xhdpi\ic_launcher_new.jpg" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_new" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-xxhdpi\ic_launcher_new.jpg" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.jpg" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\modetaris\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\modetaris\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="splash_background_orange">#FFA500</color><color name="dark_gray_background">#212121</color></file><file path="C:\Users\<USER>\modetaris\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">modetaris</string></file><file path="C:\Users\<USER>\modetaris\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Modetaris" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowActivityTransitions">false</item>
        
        <item name="android:windowBackground">@color/dark_gray_background</item>
    </style><style name="Theme.App.Starting" parent="Theme.SplashScreen">
        
        <item name="windowSplashScreenBackground">@color/splash_background_orange</item> 

        
        <item name="windowSplashScreenAnimatedIcon">@drawable/splash_background</item> 
        
        <item name="windowSplashScreenAnimationDuration">0</item> 

        
        
        <item name="postSplashScreenTheme">@style/Theme.Modetaris</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\modetaris\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\modetaris\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="provider_paths" path="C:\Users\<USER>\modetaris\app\src\main\res\xml\provider_paths.xml" qualifiers="" type="xml"/><file name="button_accept_background" path="C:\Users\<USER>\modetaris\app\src\main\res\drawable\button_accept_background.xml" qualifiers="" type="drawable"/><file name="button_decline_background" path="C:\Users\<USER>\modetaris\app\src\main\res\drawable\button_decline_background.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Users\<USER>\modetaris\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\modetaris\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\modetaris\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>