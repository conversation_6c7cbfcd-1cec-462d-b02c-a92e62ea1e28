<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- AdView at the bottom -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adViewBanner"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        app:adSize="BANNER"
        app:adUnitId="ca-app-pub-4373910379376809/6035179861">
        <!-- Make sure to replace adUnitId with your actual ID if needed -->
    </com.google.android.gms.ads.AdView>

    <!-- CustomWebView filling the space above the AdView -->
    <com.example.modetaris.CustomWebView
        android:id="@+id/webViewMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/adViewBanner" />

    <!-- Rewarded Ad Consent Dialog Overlay -->
    <LinearLayout
        android:id="@+id/rewardedAdConsentOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#CC000000"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32dp"
            android:layout_marginRight="32dp"
            android:background="@drawable/dialog_background"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Icon -->
            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="16dp"
                android:src="@android:drawable/ic_media_play"
                android:tint="#4CAF50" />

            <!-- Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="16dp"
                android:text="مشاهدة إعلان للحصول على المكافأة"
                android:textColor="#FFFFFF"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center" />

            <!-- Description -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="24dp"
                android:text="هل تريد مشاهدة إعلان قصير للحصول على المكافأة وبدء التحميل؟\n\nيمكنك اختيار 'لا' إذا كنت لا تريد مشاهدة الإعلان."
                android:textColor="#CCFFFFFF"
                android:textSize="16sp"
                android:gravity="center"
                android:lineSpacingExtra="4dp" />

            <!-- Buttons Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <!-- No Button -->
                <Button
                    android:id="@+id/btnDeclineRewardedAd"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginRight="8dp"
                    android:background="@drawable/button_decline_background"
                    android:text="لا، شكراً"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- Yes Button -->
                <Button
                    android:id="@+id/btnAcceptRewardedAd"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginLeft="8dp"
                    android:background="@drawable/button_accept_background"
                    android:text="نعم، أريد المشاهدة"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Timer Text -->
            <TextView
                android:id="@+id/tvConsentTimer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:text="يمكنك اتخاذ القرار خلال 30 ثانية"
                android:textColor="#99FFFFFF"
                android:textSize="12sp"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
