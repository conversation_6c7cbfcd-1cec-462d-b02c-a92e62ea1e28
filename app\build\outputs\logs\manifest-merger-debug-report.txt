-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:58:9-66:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:62:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:60:13-60
	android:exported
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:61:13-37
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:59:13-62
provider#com.sidimohamed.modetaris.DropDataProvider
INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:78:9-82:50
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:82:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:80:13-68
	android:exported
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:81:13-37
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:79:13-45
manifest
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:2:1-86:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7f1bc1db78be7afd443fad33f283878\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b91d173b095b76b27cd7d7591362329\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54e13e34a1b835966ab4396d6c155dd1\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d41755b046d87737969e6781e2aacc3\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ad2fa4952c65a27d545d970f7b3ce16\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a9285e9facee93b47275cc6342010a7\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50568f6bd4c48548d3d225209f3b9926\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\452b715dea7b118971dde48d3674cfb2\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a893f961db866a21714f2ba1b540e86\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a164f5bd764ffdcd3c86399960c56e42\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aac2b852c7987adc465e482d1336114\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e9fafb99af262de15b4b9f9307850cc\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d7a952c897adf1d8f980d917721cf17\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37ec3750192a0f7ab683b84a606a7457\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\caa77a8429ea4e593c2b26358237398d\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f875fef0c73a06224f5d369e6a24e63\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b6f0bed5e0bbd2331dfcab783f35fbc\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90c70fc724586b139c6699a6c74e6b58\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c5f0d2ca5eb2a53e64b7b3b9fd760c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2c6e88b2ac5e7cd3ee5dd3a7fc4da6d\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-ads:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfeebaf529411246011d89557c21ae26\transformed\play-services-ads-23.1.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:17:1-95:12
MERGED from [com.google.android.gms:play-services-ads-base:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3a3b165dd0ee207da89976c387dc264\transformed\play-services-ads-base-23.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6596ad0fb42c56f8d70546e2b1cb347a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8adb36f8adb46e2c9c3f79cd9d473767\transformed\play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\217610e6b88450385bdf93bb2aafc20f\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.ump:user-messaging-platform:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4b0e6b2f960c0b1b29110c9a739c531\transformed\user-messaging-platform-2.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0fad864749e9afa38430524e4f5d969\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4385871e4df78ed012cc3c1f214801\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1c97432aa99c9dedd6e3e6bd779cf75\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81c55b9428605876879bf01f9c2f016e\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10d3754b7bdfbcc2001d528376f5a8a8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0385a7c565ee3303ca5642564772b40a\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b4608f02ac29e503786cae7086a3ae6\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3984dc4dc17cf1906ddadc0778fbd68b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc6d38ae43d380f47e5991956ed1b06\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f62a8a8157da15235c7985b337c8c0ae\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d1aad2dcf20e5c6690051dbd41d945e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79ff1783ca20eaebd61a0cadd3d5adf3\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8461a0c48e2aa67076c78f2c3529c40c\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c272b2bae1c16d98e7c3e3f03e7d6fd8\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\708287f3ff67f43783d9d390629265de\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e169a0e421bec909580a9d628bc9cde7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61c6eb1226984653d25cc557f106ec08\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c00d5f04d41afa7b10eae289d6da7af2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6580cc819b84f2122ab9670b44be8d\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7cae3db1d10301c1628d3fbad2bf164\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bee4f25f9a93dcf06e5694a165c218\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb8864be25fae4491372a16c4340df44\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50805f74a84fae76deb712ce270e4367\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fad8b52bfc51e5778f3f19a07dafba9c\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b6c1576ac32d7347b59a8818915f6ed\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eeab6a697925e0ef89d5d2a9bf4fb3ac\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bead32a8b8b8f87eeb9bce2b79b9e1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a37205b9382aa94f048878625d6feb14\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\960354382d74700f509c427929bcf545\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4deb08fe97b89722b9a0cacdb444bef\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90d1dcb68aee1fcd83409268ffaebcd2\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7baca27908d5d2044eaa78f5f0fbef0\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7491a855d9d00ac48b70f03bf4a30cfb\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6190540078d98de4ef9e7f7d69a8c72\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1efb4c06c6d9aa3fdc2ebffd6469574f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\122d3c4045f2736e20061fe54d9839ed\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2b0724139c6c4ad3a04048367f978f7\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e593155b512d75e4d49dc98b6828f7c\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f711297febdd1e0df5629e35129eacce\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\654f1f667b715af33be846575690b54e\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\955ccee27a05689dab90396e25816b97\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f22fe9baf45fb0d77a7e71b4f33fd1c8\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c5a99a290a246fba2331479093f606\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51db767d05e5624bf9c9ea8b9b9f7f99\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c54519286289d3c1bf6958ee1dfab1e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0feb051e1fd06a535648b32521a1b2f\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ebe5c5a7708e75cd6536372defa1d55\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff7b3b76f3cefb49c4befe8ccdbf5e8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8267fb2acb6bc52fc4e70edb5d32b47d\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6bf7cf5639d60830ebb1530f9f456ed\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3de6e84d2a1cd8ae88e2d0c65feabfb\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3664cbafbc086cecca0ead547622a122\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b99b07bb9d3256b7caaccc4751a4a117\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b226e016ca9aa1f40054a6ed89084421\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd27885945adbf0b2697d56bd938b3e3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0113b728fd0d053ad8407e99525b77f6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:5:6-68
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:5:23-65
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:7:6-82
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:7:23-79
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:8:6-81
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:8:23-78
queries
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:12:5-14:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:35:5-51:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:35:5-51:15
package#com.mojang.minecraftpe
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:13:9-58
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:13:18-55
application
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:16:5-84:19
INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:16:5-84:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7f1bc1db78be7afd443fad33f283878\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7f1bc1db78be7afd443fad33f283878\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90c70fc724586b139c6699a6c74e6b58\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90c70fc724586b139c6699a6c74e6b58\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:53:5-93:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:53:5-93:19
MERGED from [com.google.android.gms:play-services-ads-base:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3a3b165dd0ee207da89976c387dc264\transformed\play-services-ads-base-23.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3a3b165dd0ee207da89976c387dc264\transformed\play-services-ads-base-23.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6596ad0fb42c56f8d70546e2b1cb347a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6596ad0fb42c56f8d70546e2b1cb347a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8adb36f8adb46e2c9c3f79cd9d473767\transformed\play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8adb36f8adb46e2c9c3f79cd9d473767\transformed\play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\217610e6b88450385bdf93bb2aafc20f\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\217610e6b88450385bdf93bb2aafc20f\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0fad864749e9afa38430524e4f5d969\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0fad864749e9afa38430524e4f5d969\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff7b3b76f3cefb49c4befe8ccdbf5e8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff7b3b76f3cefb49c4befe8ccdbf5e8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd27885945adbf0b2697d56bd938b3e3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd27885945adbf0b2697d56bd938b3e3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:25:9-52
	android:roundIcon
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:22:9-54
	android:largeHeap
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:27:9-33
	android:icon
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:20:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:23:9-35
	android:label
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:21:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:26:9-43
	android:fullBackupContent
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:19:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:28:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:17:9-35
	android:theme
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:24:9-47
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:18:9-65
activity#com.google.android.gms.ads.AdActivity
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:31:9-35:66
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:56:9-61:43
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:56:9-61:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:59:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:61:13-40
	android:configChanges
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:33:13-122
		REJECTED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:58:13-122
	android:theme
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:34:13-72
		REJECTED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:60:13-61
	tools:replace
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:35:13-64
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:32:13-65
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:38:9-40:69
	android:value
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:40:13-67
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:39:13-69
activity#com.sidimohamed.modetaris.MainActivity
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:45:9-55:20
	android:label
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:48:13-45
	android:exported
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:47:13-36
	android:theme
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:49:13-54
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:46:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:50:13-54:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:51:17-69
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:51:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:53:17-77
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:53:27-74
receiver#com.sidimohamed.modetaris.DownloadReceiver
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:69:9-75:20
	android:exported
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:71:13-37
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:70:13-45
intent-filter#action:name:android.intent.action.DOWNLOAD_COMPLETE
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:72:13-74:29
action#android.intent.action.DOWNLOAD_COMPLETE
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:73:17-81
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:73:25-79
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:63:13-65:58
	android:resource
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:65:17-55
	android:name
		ADDED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml:64:17-67
uses-sdk
INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7f1bc1db78be7afd443fad33f283878\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7f1bc1db78be7afd443fad33f283878\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b91d173b095b76b27cd7d7591362329\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b91d173b095b76b27cd7d7591362329\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54e13e34a1b835966ab4396d6c155dd1\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54e13e34a1b835966ab4396d6c155dd1\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d41755b046d87737969e6781e2aacc3\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d41755b046d87737969e6781e2aacc3\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ad2fa4952c65a27d545d970f7b3ce16\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ad2fa4952c65a27d545d970f7b3ce16\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a9285e9facee93b47275cc6342010a7\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a9285e9facee93b47275cc6342010a7\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50568f6bd4c48548d3d225209f3b9926\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50568f6bd4c48548d3d225209f3b9926\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\452b715dea7b118971dde48d3674cfb2\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\452b715dea7b118971dde48d3674cfb2\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a893f961db866a21714f2ba1b540e86\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a893f961db866a21714f2ba1b540e86\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a164f5bd764ffdcd3c86399960c56e42\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a164f5bd764ffdcd3c86399960c56e42\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aac2b852c7987adc465e482d1336114\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aac2b852c7987adc465e482d1336114\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e9fafb99af262de15b4b9f9307850cc\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e9fafb99af262de15b4b9f9307850cc\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d7a952c897adf1d8f980d917721cf17\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d7a952c897adf1d8f980d917721cf17\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37ec3750192a0f7ab683b84a606a7457\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37ec3750192a0f7ab683b84a606a7457\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\caa77a8429ea4e593c2b26358237398d\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\caa77a8429ea4e593c2b26358237398d\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f875fef0c73a06224f5d369e6a24e63\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f875fef0c73a06224f5d369e6a24e63\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b6f0bed5e0bbd2331dfcab783f35fbc\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b6f0bed5e0bbd2331dfcab783f35fbc\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90c70fc724586b139c6699a6c74e6b58\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90c70fc724586b139c6699a6c74e6b58\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c5f0d2ca5eb2a53e64b7b3b9fd760c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c5f0d2ca5eb2a53e64b7b3b9fd760c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2c6e88b2ac5e7cd3ee5dd3a7fc4da6d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2c6e88b2ac5e7cd3ee5dd3a7fc4da6d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfeebaf529411246011d89557c21ae26\transformed\play-services-ads-23.1.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfeebaf529411246011d89557c21ae26\transformed\play-services-ads-23.1.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3a3b165dd0ee207da89976c387dc264\transformed\play-services-ads-base-23.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3a3b165dd0ee207da89976c387dc264\transformed\play-services-ads-base-23.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6596ad0fb42c56f8d70546e2b1cb347a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6596ad0fb42c56f8d70546e2b1cb347a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8adb36f8adb46e2c9c3f79cd9d473767\transformed\play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8adb36f8adb46e2c9c3f79cd9d473767\transformed\play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\217610e6b88450385bdf93bb2aafc20f\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\217610e6b88450385bdf93bb2aafc20f\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.ump:user-messaging-platform:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4b0e6b2f960c0b1b29110c9a739c531\transformed\user-messaging-platform-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4b0e6b2f960c0b1b29110c9a739c531\transformed\user-messaging-platform-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0fad864749e9afa38430524e4f5d969\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0fad864749e9afa38430524e4f5d969\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4385871e4df78ed012cc3c1f214801\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f4385871e4df78ed012cc3c1f214801\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1c97432aa99c9dedd6e3e6bd779cf75\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1c97432aa99c9dedd6e3e6bd779cf75\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81c55b9428605876879bf01f9c2f016e\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81c55b9428605876879bf01f9c2f016e\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10d3754b7bdfbcc2001d528376f5a8a8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10d3754b7bdfbcc2001d528376f5a8a8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0385a7c565ee3303ca5642564772b40a\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0385a7c565ee3303ca5642564772b40a\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b4608f02ac29e503786cae7086a3ae6\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b4608f02ac29e503786cae7086a3ae6\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3984dc4dc17cf1906ddadc0778fbd68b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3984dc4dc17cf1906ddadc0778fbd68b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc6d38ae43d380f47e5991956ed1b06\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc6d38ae43d380f47e5991956ed1b06\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f62a8a8157da15235c7985b337c8c0ae\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f62a8a8157da15235c7985b337c8c0ae\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d1aad2dcf20e5c6690051dbd41d945e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d1aad2dcf20e5c6690051dbd41d945e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79ff1783ca20eaebd61a0cadd3d5adf3\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79ff1783ca20eaebd61a0cadd3d5adf3\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8461a0c48e2aa67076c78f2c3529c40c\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8461a0c48e2aa67076c78f2c3529c40c\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c272b2bae1c16d98e7c3e3f03e7d6fd8\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c272b2bae1c16d98e7c3e3f03e7d6fd8\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\708287f3ff67f43783d9d390629265de\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\708287f3ff67f43783d9d390629265de\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e169a0e421bec909580a9d628bc9cde7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e169a0e421bec909580a9d628bc9cde7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61c6eb1226984653d25cc557f106ec08\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61c6eb1226984653d25cc557f106ec08\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c00d5f04d41afa7b10eae289d6da7af2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c00d5f04d41afa7b10eae289d6da7af2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6580cc819b84f2122ab9670b44be8d\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6580cc819b84f2122ab9670b44be8d\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7cae3db1d10301c1628d3fbad2bf164\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7cae3db1d10301c1628d3fbad2bf164\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bee4f25f9a93dcf06e5694a165c218\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bee4f25f9a93dcf06e5694a165c218\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb8864be25fae4491372a16c4340df44\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb8864be25fae4491372a16c4340df44\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50805f74a84fae76deb712ce270e4367\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50805f74a84fae76deb712ce270e4367\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fad8b52bfc51e5778f3f19a07dafba9c\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fad8b52bfc51e5778f3f19a07dafba9c\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b6c1576ac32d7347b59a8818915f6ed\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b6c1576ac32d7347b59a8818915f6ed\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eeab6a697925e0ef89d5d2a9bf4fb3ac\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eeab6a697925e0ef89d5d2a9bf4fb3ac\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bead32a8b8b8f87eeb9bce2b79b9e1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bead32a8b8b8f87eeb9bce2b79b9e1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a37205b9382aa94f048878625d6feb14\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a37205b9382aa94f048878625d6feb14\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\960354382d74700f509c427929bcf545\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\960354382d74700f509c427929bcf545\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4deb08fe97b89722b9a0cacdb444bef\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4deb08fe97b89722b9a0cacdb444bef\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90d1dcb68aee1fcd83409268ffaebcd2\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90d1dcb68aee1fcd83409268ffaebcd2\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7baca27908d5d2044eaa78f5f0fbef0\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7baca27908d5d2044eaa78f5f0fbef0\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7491a855d9d00ac48b70f03bf4a30cfb\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7491a855d9d00ac48b70f03bf4a30cfb\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6190540078d98de4ef9e7f7d69a8c72\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6190540078d98de4ef9e7f7d69a8c72\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1efb4c06c6d9aa3fdc2ebffd6469574f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1efb4c06c6d9aa3fdc2ebffd6469574f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\122d3c4045f2736e20061fe54d9839ed\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\122d3c4045f2736e20061fe54d9839ed\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2b0724139c6c4ad3a04048367f978f7\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2b0724139c6c4ad3a04048367f978f7\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e593155b512d75e4d49dc98b6828f7c\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e593155b512d75e4d49dc98b6828f7c\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f711297febdd1e0df5629e35129eacce\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f711297febdd1e0df5629e35129eacce\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\654f1f667b715af33be846575690b54e\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\654f1f667b715af33be846575690b54e\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\955ccee27a05689dab90396e25816b97\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\955ccee27a05689dab90396e25816b97\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f22fe9baf45fb0d77a7e71b4f33fd1c8\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f22fe9baf45fb0d77a7e71b4f33fd1c8\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c5a99a290a246fba2331479093f606\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c5a99a290a246fba2331479093f606\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51db767d05e5624bf9c9ea8b9b9f7f99\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51db767d05e5624bf9c9ea8b9b9f7f99\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c54519286289d3c1bf6958ee1dfab1e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c54519286289d3c1bf6958ee1dfab1e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0feb051e1fd06a535648b32521a1b2f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0feb051e1fd06a535648b32521a1b2f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ebe5c5a7708e75cd6536372defa1d55\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ebe5c5a7708e75cd6536372defa1d55\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff7b3b76f3cefb49c4befe8ccdbf5e8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff7b3b76f3cefb49c4befe8ccdbf5e8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8267fb2acb6bc52fc4e70edb5d32b47d\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8267fb2acb6bc52fc4e70edb5d32b47d\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6bf7cf5639d60830ebb1530f9f456ed\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6bf7cf5639d60830ebb1530f9f456ed\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3de6e84d2a1cd8ae88e2d0c65feabfb\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3de6e84d2a1cd8ae88e2d0c65feabfb\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3664cbafbc086cecca0ead547622a122\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3664cbafbc086cecca0ead547622a122\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b99b07bb9d3256b7caaccc4751a4a117\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b99b07bb9d3256b7caaccc4751a4a117\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b226e016ca9aa1f40054a6ed89084421\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b226e016ca9aa1f40054a6ed89084421\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd27885945adbf0b2697d56bd938b3e3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd27885945adbf0b2697d56bd938b3e3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0113b728fd0d053ad8407e99525b77f6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0113b728fd0d053ad8407e99525b77f6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfeebaf529411246011d89557c21ae26\transformed\play-services-ads-23.1.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\modetaris\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\671d48c9433331bea8b1966e0b4b9764\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec2559fba47ed7c9f9c01a4dd151b59\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:25:22-76
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6596ad0fb42c56f8d70546e2b1cb347a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6596ad0fb42c56f8d70546e2b1cb347a\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:26:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:41:23-71
data
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:43:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:43:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:48:21-87
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:63:9-68:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:65:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:66:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:68:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:67:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:64:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:70:9-74:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:72:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:73:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:74:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:71:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:76:9-80:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:79:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:80:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:78:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:77:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:81:9-88:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:83:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:85:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:84:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:88:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:87:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:86:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:82:13-82
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:90:9-92:62
	android:resource
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:92:13-59
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd87f90d2614e61c3e65778bffadc9\transformed\play-services-ads-lite-23.1.0\AndroidManifest.xml:91:13-65
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1984713afaf7e95791b7113f0c3d099\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90631f36e1cf0848fd1f0de7e40680c5\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\828443dfe1a0469ecc410a4d0bc8bbd4\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff7b3b76f3cefb49c4befe8ccdbf5e8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ff7b3b76f3cefb49c4befe8ccdbf5e8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8680e76df64faf1b065385bac4f2f9f4\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\142c99354746f9355dc94fe1284b1dbe\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6c15af8efe443eeefbdd0499635d670\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067dc8c1d5b35011d85467915815658b\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.sidimohamed.modetaris.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.sidimohamed.modetaris.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65feab96c843e558e67ad51c84b27304\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2952e9c1b45875614f967c149f72a19e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ecde923eeabf147f1375df62ab5c9ca\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
