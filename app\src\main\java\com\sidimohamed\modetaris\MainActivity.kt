package com.sidimohamed.modetaris

import android.annotation.SuppressLint
import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.os.Environment // Keep for DIRECTORY_DOWNLOADS constant
import android.os.Handler // Import Handler
import android.os.Looper // Import Looper
import android.os.CountDownTimer // Import CountDownTimer for consent dialog
import android.util.Log
import android.webkit.JavascriptInterface
import android.webkit.WebSettings
import android.webkit.WebView // Import standard WebView for WebViewClient parameter type
// REMOVED: Timer, TimerTask imports
// REMOVED: import android.webkit.WebView (using CustomWebView instead) - Note: Still need standard WebView for parameter types
import android.webkit.WebViewClient
import android.widget.RelativeLayout // Import RelativeLayout
import android.widget.Toast
import android.widget.Button // Import Button for consent dialog
import android.widget.LinearLayout // Import LinearLayout for consent dialog
import android.widget.TextView // Import TextView for consent dialog
import android.view.View // Import View for visibility constants
// REMOVED: Permission imports (ActivityCompat, ContextCompat, Manifest, PackageManager)
import android.os.Build // Keep for SDK version check
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.pm.PackageManager
import android.Manifest
import androidx.activity.ComponentActivity
import androidx.activity.enableEdgeToEdge
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import android.content.SharedPreferences // Import SharedPreferences
import android.util.TypedValue // Import for DP calculation
import android.graphics.Color // Import for Color parsing
import android.provider.Settings // Import Settings for app settings intent
import android.view.ViewGroup // Import ViewGroup for finding views
import android.view.WindowManager // Import WindowManager for fullscreen flags
import androidx.core.view.ViewCompat // Import ViewCompat for window insets
import com.google.android.material.snackbar.Snackbar // Import Snackbar
import java.io.File
import java.net.URLDecoder
import java.util.concurrent.ConcurrentHashMap
import org.json.JSONObject // Import JSONObject for storing multiple pending updates
import java.util.HashSet // Import HashSet for storing downloaded mod IDs

// Import AdMob classes
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdView // Import AdView for Banner
import com.google.android.gms.ads.OnUserEarnedRewardListener // Still needed for Rewarded Ad
import com.google.android.gms.ads.rewarded.RewardedAd // Import for Rewarded Ad
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback // Import for Rewarded Ad
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen // Import for Splash Screen
import androidx.core.view.WindowCompat // Import for immersive mode
import androidx.core.view.WindowInsetsCompat // Import for immersive mode
import androidx.core.view.WindowInsetsControllerCompat // Import for immersive mode
import androidx.lifecycle.lifecycleScope // Import for coroutines
import kotlinx.coroutines.launch // For launching coroutines
// REMOVED: kotlinx.serialization.Serializable (No longer needed after AdConfig removal)

// REMOVED: Supabase Imports

// REMOVED: ScrollListener implementation

// REMOVED: Data class for Ad Configuration from Supabase (AdConfig)

class MainActivity : ComponentActivity() {

    // REMOVED: STORAGE_PERMISSION_REQUEST_CODE companion object

    private lateinit var webView: WebView
    private lateinit var adViewBanner: AdView // Declare AdView for Banner
    // REMOVED: private lateinit var swipeRefreshLayout: SwipeRefreshLayout
    private lateinit var downloadManager: DownloadManager
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var webAppInterface: WebAppInterface

    // Rewarded Ad Consent Dialog UI Elements
    private lateinit var rewardedAdConsentOverlay: LinearLayout
    private lateinit var btnAcceptRewardedAd: Button
    private lateinit var btnDeclineRewardedAd: Button
    private lateinit var tvConsentTimer: TextView
    private var consentTimer: CountDownTimer? = null
    // Map to track active downloads initiated by this activity instance
    private val activeDownloads = ConcurrentHashMap<Long, String>() // downloadId -> modId
    // Handler for polling
    private val pollingHandler = Handler(Looper.getMainLooper())
    // Handler for auto-cleanup
    private val cleanupHandler = Handler(Looper.getMainLooper())
    // Runnable for auto-cleanup
    private val cleanupRunnable = Runnable { performCleanupOfOldDownloads() }

    // AdMob Rewarded Ad
    private var mRewardedAd: RewardedAd? = null
    // Default/Fallback Ad Unit IDs (Constants)
    private companion object {
        const val DEFAULT_REWARDED_AD_UNIT_ID = "ca-app-pub-4373910379376809/6427121599"
        const val DEFAULT_BANNER_AD_UNIT_ID = "ca-app-pub-4373910379376809/6035179861"
    }
    private var currentRewardedAdUnitId: String = DEFAULT_REWARDED_AD_UNIT_ID
    private var isRewardedAdEnabled: Boolean = true // Default/Fallback

    // Banner Ad
    private var currentBannerAdUnitId: String = DEFAULT_BANNER_AD_UNIT_ID
    private var isBannerAdEnabled: Boolean = true // Default/Fallback
    private var bannerAdPosition: String = "bottom" // Default/Fallback

    private var pendingDownloadUrl: String? = null // To store URL while ad is showing
    private var pendingModId: String? = null       // To store Mod ID
    private var pendingModName: String? = null     // To store Mod Name

    // Flag to track the first touch event after page load
    private var isFirstTouchEventAfterLoad = true

    // Keys for SharedPreferences
    private val PREFS_NAME = "ModetarisPrefs"
    private val PENDING_DOWNLOADS_KEY = "pending_downloads" // Keep if still used elsewhere
    private val DOWNLOADED_MODS_KEY = "downloaded_mod_ids" // New key for tracking counted downloads
    private val MOD_DOWNLOAD_TIMESTAMPS_KEY = "mod_download_timestamps" // Key for tracking download timestamps
    private val AUTO_DELETE_ENABLED_KEY = "auto_delete_enabled" // Key for auto-delete feature toggle

    // تحسين ثوابت الحذف التلقائي
    private val AUTO_DELETE_DELAY_MS = 1 * 60 * 60 * 1000L // ساعة واحدة بدلاً من ساعتين لتوفير المساحة
    private val AUTO_CLEANUP_INTERVAL_MS = 15 * 60 * 1000L // فحص كل 15 دقيقة بدلاً من 30

    // REMOVED: Supabase Client and related properties


    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        // Keep the splash screen visible until initialization is complete
        val splashScreen = installSplashScreen()
        splashScreen.setKeepOnScreenCondition { true } // Keep splash screen visible

        super.onCreate(savedInstanceState)
        // enableEdgeToEdge() // Disable edge-to-edge as we are manually controlling system bars

        // Initialize services in background thread to prevent ANR
        lifecycleScope.launch {
            // Initialize Mobile Ads SDK
            MobileAds.initialize(this@MainActivity) {}
            Log.d("MainActivity", "MobileAds SDK Initialized.")

            // Apply default ad settings (can be done before view inflation)
            isRewardedAdEnabled = true // Default
            currentRewardedAdUnitId = DEFAULT_REWARDED_AD_UNIT_ID
            isBannerAdEnabled = true // Default
            currentBannerAdUnitId = DEFAULT_BANNER_AD_UNIT_ID
            bannerAdPosition = "bottom" // Default

            // REMOVED: Explicit window background setting that overwrites splash screen
            // val darkGrayColor = android.graphics.Color.parseColor("#21221f")
            // window.setBackgroundDrawable(ColorDrawable(darkGrayColor))

            downloadManager = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            sharedPreferences = getSharedPreferences("ModetarisPrefs", Context.MODE_PRIVATE)

            // Create cache directories if they don't exist
            createCacheDirectories()

            // Signal that background initialization is complete
            runOnUiThread {
                // --- Load the layout file ---
                setContentView(R.layout.activity_main) // Load the XML layout

                // Allow the splash screen to be removed
                splashScreen.setKeepOnScreenCondition { false }

                // --- Find WebView and AdView from the layout ---
                webView = findViewById(R.id.webViewMain) // Find WebView by ID
                adViewBanner = findViewById(R.id.adViewBanner) // Find AdView by ID

                // --- Initialize Rewarded Ad Consent Dialog UI Elements ---
                rewardedAdConsentOverlay = findViewById(R.id.rewardedAdConsentOverlay)
                btnAcceptRewardedAd = findViewById(R.id.btnAcceptRewardedAd)
                btnDeclineRewardedAd = findViewById(R.id.btnDeclineRewardedAd)
                tvConsentTimer = findViewById(R.id.tvConsentTimer)

                // Setup consent dialog button listeners
                setupConsentDialogListeners()

                // Ensure consent dialog is hidden initially
                rewardedAdConsentOverlay.visibility = View.GONE

                // --- ADS INITIALIZATION MOVED HERE (After views are found) ---
                // Load Rewarded Ad
                if (isRewardedAdEnabled) {
                    Log.d("MainActivity", "Rewarded Ad is ENABLED (default). Unit ID: $currentRewardedAdUnitId")
                    loadRewardedAd()
                } else {
                    Log.d("MainActivity", "Rewarded Ad is DISABLED (default).")
                    mRewardedAd = null
                }

                // Load Banner Ad
                if (isBannerAdEnabled) {
                    Log.d("MainActivity", "Banner Ad is ENABLED (default). Unit ID from XML, Position: $bannerAdPosition") // Updated Log
                    adViewBanner.visibility = View.VISIBLE
                    // adViewBanner.adUnitId = currentBannerAdUnitId // REMOVED: Ad Unit ID is set in XML

                    val layoutParams = adViewBanner.layoutParams as RelativeLayout.LayoutParams
                    val webViewLayoutParams = webView.layoutParams as RelativeLayout.LayoutParams

                    if (bannerAdPosition == "top") {
                        layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
                        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP)
                        webViewLayoutParams.removeRule(RelativeLayout.ABOVE)
                        webViewLayoutParams.addRule(RelativeLayout.BELOW, adViewBanner.id)
                    } else { // Default to bottom
                        layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_TOP)
                        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
                        webViewLayoutParams.removeRule(RelativeLayout.BELOW)
                        webViewLayoutParams.addRule(RelativeLayout.ABOVE, adViewBanner.id)
                    }
                    adViewBanner.layoutParams = layoutParams
                    webView.layoutParams = webViewLayoutParams

                    val adRequestBanner = AdRequest.Builder().build()
                    adViewBanner.loadAd(adRequestBanner)
                    Log.d("MainActivity", "Banner Ad request sent with default settings.")
                } else {
                    Log.d("MainActivity", "Banner Ad is DISABLED (default).")
                    adViewBanner.visibility = View.GONE
                    val webViewLayoutParams = webView.layoutParams as RelativeLayout.LayoutParams
                    webViewLayoutParams.removeRule(RelativeLayout.ABOVE)
                    webViewLayoutParams.removeRule(RelativeLayout.BELOW)
                    webView.layoutParams = webViewLayoutParams
                }
                // --- END ADS INITIALIZATION ---

                // --- REMOVED: SwipeRefreshLayout setup and configuration ---

                // Hide system bars for immersive mode *after* setting content view
                hideSystemUI()

                // --- Setup scroll detection ---
                webView.setOnScrollChangeListener { _, _, scrollY, _, _ ->
                    val isAtTop = scrollY == 0
                    // Handle scroll state if needed
                    Log.d("MainActivity", "WebView scroll changed: isAtTop=$isAtTop")
                }

                val webSettings: WebSettings = webView.settings
                webSettings.javaScriptEnabled = true
                webSettings.domStorageEnabled = true
                webSettings.allowFileAccess = true // Keep true for file:/// URLs and potentially FileProvider access
                webSettings.allowContentAccess = true // Keep true
                // تحسين إعدادات التخزين المؤقت
                webSettings.cacheMode = WebSettings.LOAD_DEFAULT
                // Note: setAppCacheEnabled, setAppCachePath, and setAppCacheMaxSize are deprecated
                // and removed in newer Android versions. Modern WebView handles caching automatically.

                // تحسين قاعدة البيانات
                webSettings.databaseEnabled = true
                webSettings.domStorageEnabled = true

                // تحسينات إضافية للأداء
                // Note: setRenderPriority and setEnableSmoothTransition are deprecated
                // Modern WebView handles rendering optimization automatically

                // تحسين عرض الصفحة
                webView.isVerticalScrollBarEnabled = false
                webView.isHorizontalScrollBarEnabled = false
                webView.overScrollMode = android.view.View.OVER_SCROLL_NEVER

                // تحسينات إضافية للأداء
                webView.isScrollbarFadingEnabled = true
                webView.scrollBarDefaultDelayBeforeFade = 300
                webView.scrollBarFadeDuration = 300

                webAppInterface = WebAppInterface(this@MainActivity) // Initialize the member variable
                webView.addJavascriptInterface(webAppInterface, "AndroidInterface")

                webView.setBackgroundColor(android.graphics.Color.TRANSPARENT)

                // Explicitly enable hardware acceleration (should be default, but for safety)
                // webView.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                // Log.d("MainActivity", "Set WebView layer type to HARDWARE (explicitly)")

                // Hardware acceleration is generally preferred (default)
                // webView.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null) // Example if needed explicitly
                Log.d("MainActivity", "Using default WebView layer type (likely HARDWARE)")

                // --- Reverted: Removed Software Layer Type experiment ---
                // webView.setLayerType(android.view.View.LAYER_TYPE_SOFTWARE, null)
                // Log.d("MainActivity", "Set WebView layer type to SOFTWARE for testing")
                // --- END Reverted ---


                // --- REMOVED: Focus properties ---
                // webView.isFocusable = true
                // webView.isFocusableInTouchMode = true
                // --- END REMOVED ---

                // Ensure WebView is focusable before loading content
                webView.isFocusable = true
                webView.isFocusableInTouchMode = true
                Log.d("MainActivity", "Set WebView focusable and focusableInTouchMode to true")

                // --- Use Custom WebViewClient (Removed simulated click) ---
                webView.webViewClient = object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        Log.d("MainActivity", "WebView onPageFinished: $url")
                        // Execute JavaScript to attach the listener AFTER page is finished
                        view?.evaluateJavascript("javascript:attachDelayedEventListeners();", null)
                        Log.d("MainActivity", "onPageFinished: Called attachDelayedEventListeners()")

                        // Request focus after a short delay to potentially fix first click issue
                        Handler(Looper.getMainLooper()).postDelayed({
                            Log.d("MainActivity", "Requesting focus (FOCUS_DOWN) for WebView after delay.")
                            // Try focusing downwards into the view hierarchy
                            view?.requestFocus(android.view.View.FOCUS_DOWN)
                        }, 300) // 300ms delay

                        // Reset the first touch flag whenever a page finishes loading
                        isFirstTouchEventAfterLoad = true
                        Log.d("MainActivity", "onPageFinished: Reset isFirstTouchEventAfterLoad to true")
                    }

                    // --- ADDED: Handle external links ---
                    override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                        Log.d("MainActivity", "shouldOverrideUrlLoading: Intercepted URL: $url")
                        if (url == null) return false // Let WebView handle null URLs

                        val uri = Uri.parse(url)
                        return when (uri.scheme) {
                            "http", "https" -> {
                                // Open web links externally
                                Log.i("MainActivity", "Opening external web link: $url")
                                val intent = Intent(Intent.ACTION_VIEW, uri)
                                try {
                                    startActivity(intent)
                                    true // Indicate we've handled the URL
                                } catch (e: Exception) {
                                    Log.e("MainActivity", "Failed to open external web link", e)
                                    Toast.makeText(this@MainActivity, "لا يمكن فتح الرابط.", Toast.LENGTH_SHORT).show()
                                    true // Still handled, even if failed
                                }
                            }
                            "mailto" -> {
                                // Open email links externally
                                Log.i("MainActivity", "Opening mailto link: $url")
                                val intent = Intent(Intent.ACTION_SENDTO, uri)
                                try {
                                    startActivity(intent)
                                    true // Indicate we've handled the URL
                                } catch (e: Exception) {
                                    Log.e("MainActivity", "Failed to open mailto link", e)
                                    Toast.makeText(this@MainActivity, "لا يمكن فتح تطبيق البريد.", Toast.LENGTH_SHORT).show()
                                    true // Still handled, even if failed
                                }
                            }
                            // Add other schemes like "tel:", "sms:" if needed
                            else -> {
                                // Let the WebView handle other schemes (e.g., file:, data:, javascript:)
                                Log.d("MainActivity", "Letting WebView handle scheme: ${uri.scheme}")
                                false // Indicate WebView should handle it
                            }
                        }
                    }
                    // --- END ADDED ---
                }
                // --- END Custom WebViewClient ---

                // Clear cache before loading URL
                webView.clearCache(true)
                Log.d("MainActivity", "WebView cache cleared.")

                webView.loadUrl("file:///android_asset/index.html") // Changed back to index.html

                // --- REMOVED: Delayed focus request ---
                // Handler(Looper.getMainLooper()).postDelayed({
                //     webView.requestFocus()
                //     Log.d("MainActivity", "Delayed focus requested for WebView.")
                // }, 300) // Delay for 300 milliseconds (adjust if needed)

                // --- NATIVE TOUCH LISTENER REMOVED ---
                // The setOnTouchListener block was removed here to fix the first click issue.

                // Ad loading is now handled above, after views are initialized

                // Start the auto-cleanup service for downloaded mods
                scheduleNextCleanup()
                Log.d("MainActivity", "Auto-cleanup service started. Files will be deleted after ${AUTO_DELETE_DELAY_MS / 1000 / 60 / 60} hours")
            } // End of runOnUiThread
        } // End of lifecycleScope.launch

    } // Closing brace for onCreate

    // REMOVED: fetchAdConfiguration function
    // REMOVED: applyAdSettings function

    // Setup consent dialog button listeners
    private fun setupConsentDialogListeners() {
        btnAcceptRewardedAd.setOnClickListener {
            Log.d("MainActivity", "User accepted rewarded ad consent")
            hideConsentDialog()
            proceedWithRewardedAd()
        }

        btnDeclineRewardedAd.setOnClickListener {
            Log.d("MainActivity", "User declined rewarded ad consent")
            hideConsentDialog()
            proceedWithoutRewardedAd()
        }
    }

    // Show consent dialog for rewarded ad
    private fun showConsentDialog() {
        Log.d("MainActivity", "showConsentDialog() called")

        runOnUiThread {
            Log.d("MainActivity", "Setting consent dialog visibility to VISIBLE")
            rewardedAdConsentOverlay.visibility = View.VISIBLE
            Log.d("MainActivity", "Consent dialog should now be visible")

            // Start countdown timer (30 seconds)
            consentTimer?.cancel() // Cancel any existing timer
            consentTimer = object : CountDownTimer(30000, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    val secondsLeft = millisUntilFinished / 1000
                    tvConsentTimer.text = "يمكنك اتخاذ القرار خلال $secondsLeft ثانية"
                }

                override fun onFinish() {
                    Log.d("MainActivity", "Consent dialog timer finished - auto declining")
                    hideConsentDialog()
                    proceedWithoutRewardedAd()
                }
            }
            consentTimer?.start()
        }
    }

    // Hide consent dialog
    private fun hideConsentDialog() {
        Log.d("MainActivity", "Hiding rewarded ad consent dialog")

        runOnUiThread {
            consentTimer?.cancel()
            rewardedAdConsentOverlay.visibility = View.GONE
        }
    }

    // Proceed with showing rewarded ad after user consent
    private fun proceedWithRewardedAd() {
        Log.d("MainActivity", "Proceeding with rewarded ad after user consent")

        // Check if Rewarded Ad is loaded and show it
        if (mRewardedAd != null) {
            Log.i("MainActivity", "Rewarded Ad is loaded. Setting callbacks and showing ad...")
            showRewardedAdInternal()
        } else {
            Log.w("MainActivity", "Rewarded Ad not loaded when user consented. Proceeding directly with download.")
            Toast.makeText(this, "الإعلان غير جاهز، سيتم بدء التحميل مباشرة", Toast.LENGTH_SHORT).show()
            proceedWithoutRewardedAd()
        }
    }

    // Proceed without rewarded ad (user declined or ad not available)
    private fun proceedWithoutRewardedAd() {
        Log.d("MainActivity", "Proceeding without rewarded ad")

        // Start download directly without ad reward
        if (pendingDownloadUrl != null && pendingModId != null && pendingModName != null) {
            initiateActualDownload(pendingModId!!, pendingModName!!, pendingDownloadUrl!!)
        } else {
            Log.w("MainActivity", "No pending download info found when proceeding without ad")
        }

        clearPendingDownloadInfo()

        // Try to load the next ad for future use
        if (isRewardedAdEnabled) {
            loadRewardedAd()
        }
    }

    // Internal function to show rewarded ad (after user consent)
    private fun showRewardedAdInternal() {
        Log.i("MainActivity", "Showing rewarded ad after user consent")

        // إخفاء واجهة النظام لضمان عرض الإعلان بملء الشاشة
        hideSystemUI()

        // إخفاء الشريط العلوي الثابت في WebView باستخدام JavaScript - محسّن
        webView.evaluateJavascript("javascript:(function() { " +
                "// إخفاء جميع العناصر الثابتة بشكل شامل" +
                "var allFixedElements = document.querySelectorAll(" +
                "  'header, " +                                // جميع وسوم header
                "  .navbar, " +                                // جميع عناصر navbar
                "  .header, " +                                // جميع عناصر header
                "  [class*=\"header\"], " +                    // أي عنصر يحتوي على كلمة header في اسم الفئة
                "  [id*=\"header\"], " +                       // أي عنصر يحتوي على كلمة header في المعرف
                "  [class*=\"nav\"], " +                       // أي عنصر يحتوي على كلمة nav في اسم الفئة
                "  [id*=\"nav\"], " +                          // أي عنصر يحتوي على كلمة nav في المعرف
                "  [class*=\"top\"], " +                       // أي عنصر يحتوي على كلمة top في اسم الفئة
                "  [id*=\"top\"], " +                          // أي عنصر يحتوي على كلمة top في المعرف
                "  [class*=\"bar\"], " +                       // أي عنصر يحتوي على كلمة bar في اسم الفئة
                "  [id*=\"bar\"], " +                          // أي عنصر يحتوي على كلمة bar في المعرف
                "  [style*=\"position: fixed\"], " +           // أي عنصر له نمط position: fixed
                "  [style*=\"position:fixed\"], " +            // بديل بدون مسافة
                "  [style*=\"position: sticky\"], " +          // أي عنصر له نمط position: sticky
                "  [style*=\"position:sticky\"], " +           // بديل بدون مسافة
                "  [style*=\"position: absolute\"], " +        // أي عنصر له نمط position: absolute في الأعلى
                "  [style*=\"position:absolute\"], " +         // بديل بدون مسافة
                "  [style*=\"top: 0\"], " +                    // أي عنصر له نمط top: 0
                "  [style*=\"top:0\"], " +                     // بديل بدون مسافة
                "  [style*=\"z-index\"], " +                   // أي عنصر له z-index عالي
                "  .fixed, " +                                 // فئة fixed
                "  .fixed-top, " +                             // فئة fixed-top
                "  .sticky-top, " +                            // فئة sticky-top
                "  .navbar-fixed-top, " +                      // فئة navbar-fixed-top
                "  .app-header, " +                            // فئة app-header
                "  .site-header, " +                           // فئة site-header
                "  .page-header, " +                           // فئة page-header
                "  .main-header, " +                           // فئة main-header
                "  .top-bar, " +                               // فئة top-bar
                "  .navigation-bar, " +                        // فئة navigation-bar
                "  .toolbar, " +                               // فئة toolbar
                "  .app-bar, " +                               // فئة app-bar
                "  .action-bar, " +                            // فئة action-bar
                "  .top-fixed-bar, " +                         // الشريط العلوي الثابت
                "  .drawer, " +                                // الشريط الجانبي
                "  .drawer-overlay, " +                        // خلفية الشريط الجانبي
                "  .modal, " +                                 // النافذة المنبثقة
                "  .download-bar, " +                          // شريط التحميل
                "  .network-status-indicator, " +              // مؤشر حالة الشبكة
                "  .offline-banner, " +                        // لافتة عدم الاتصال
                "  .floating-icon, " +                         // أيقونة الاشتراك العائمة
                "  #install-instructions-modal, " +            // نافذة تعليمات التثبيت
                "  #image-zoom-modal, " +                      // نافذة تكبير الصورة
                "  .banner-ad-modal" +                         // نافذة الإعلان البانر
                "');" +

                "// إخفاء جميع العناصر المحددة" +
                "for(var i=0; i<allFixedElements.length; i++) { " +
                "    var element = allFixedElements[i];" +
                "    // حفظ الحالة الأصلية للعنصر لاستعادتها لاحقاً" +
                "    element.setAttribute('data-original-display', element.style.display || 'block');" +
                "    element.style.display = 'none';" +
                "    element.style.visibility = 'hidden';" +
                "    element.style.opacity = '0';" +
                "    element.style.pointerEvents = 'none';" +
                "    console.log('Hidden fixed element:', element.tagName, element.className || element.id || 'unknown');" +
                "}" +

                "// إخفاء أي عنصر مطلق الموضع في الأعلى" +
                "var topElements = document.querySelectorAll('*');" +
                "for(var i=0; i<topElements.length; i++) {" +
                "    var element = topElements[i];" +
                "    var style = window.getComputedStyle(element);" +
                "    if((style.position === 'fixed' || style.position === 'sticky' || " +
                "       (style.position === 'absolute' && (style.top === '0px' || parseInt(style.top) < 50))) && " +
                "       style.display !== 'none') {" +
                "        element.setAttribute('data-original-display', element.style.display || 'block');" +
                "        element.style.display = 'none';" +
                "        element.style.visibility = 'hidden';" +
                "        element.style.opacity = '0';" +
                "        element.style.pointerEvents = 'none';" +
                "        console.log('Hidden computed fixed element:', element.tagName, element.className || element.id || 'unknown');" +
                "    }" +
                "}" +

                "// إضافة CSS مؤقت لمنع أي عناصر ثابتة من الظهور" +
                "var style = document.createElement('style');" +
                "style.id = 'temp-ad-styles';" +
                "style.innerHTML = 'header, .navbar, .header, .fixed, .fixed-top, .sticky-top, .navbar-fixed-top, " +
                "                  [style*=\"position: fixed\"], [style*=\"position:fixed\"], " +
                "                  [style*=\"position: sticky\"], [style*=\"position:sticky\"], " +
                "                  [style*=\"top: 0\"], [style*=\"top:0\"], " +
                "                  [class*=\"header\"], [id*=\"header\"], " +
                "                  [class*=\"nav\"], [id*=\"nav\"], " +
                "                  [class*=\"top\"], [id*=\"top\"], " +
                "                  [class*=\"bar\"], [id*=\"bar\"], " +
                "                  .top-fixed-bar, .drawer, .drawer-overlay, .modal, .download-bar, " +
                "                  .network-status-indicator, .offline-banner, .floating-icon, " +
                "                  #install-instructions-modal, #image-zoom-modal, .banner-ad-modal " +
                "                  { display: none !important; visibility: hidden !important; opacity: 0 !important; pointer-events: none !important; z-index: -9999 !important; }';" +
                "document.head.appendChild(style);" +

                "console.log('Added temporary CSS to hide all fixed elements');" +
                "})();", null)
        Log.d("MainActivity", "Executed enhanced JS to hide all fixed elements before showing ad")

        // Set FullScreenContentCallback before showing
        mRewardedAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdClicked() {
                Log.d("MainActivity", "Rewarded Ad was clicked.")
            }

            override fun onAdDismissedFullScreenContent() {
                Log.d("MainActivity", "Rewarded Ad dismissed.")

                // إعادة عرض واجهة النظام بعد إغلاق الإعلان
                showSystemUI()

                // إعادة عرض جميع العناصر المخفية
                runOnUiThread {
                    // إعادة عرض WebView
                    webView.visibility = View.VISIBLE

                    // إعادة عرض البانر الإعلاني
                    if (isBannerAdEnabled) {
                        adViewBanner.visibility = View.VISIBLE
                    }

                    // إعادة عرض الشريط العلوي الثابت في WebView باستخدام JavaScript - محسّن
                    webView.evaluateJavascript("javascript:(function() { " +
                            "// إزالة CSS المؤقت أولاً" +
                            "var tempStyle = document.getElementById('temp-ad-styles');" +
                            "if(tempStyle) {" +
                            "    tempStyle.parentNode.removeChild(tempStyle);" +
                            "    console.log('Removed temporary CSS styles');" +
                            "}" +
                            "})();", null)
                    Log.d("MainActivity", "Restored WebView UI elements after ad dismissal")
                }

                // Clear pending download info after ad dismissal
                clearPendingDownloadInfo()

                // Load next ad for future use
                if (isRewardedAdEnabled) {
                    loadRewardedAd()
                }
            }

            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                Log.e("MainActivity", "Rewarded Ad failed to show: ${adError.message}")

                // إعادة عرض واجهة النظام في حالة فشل عرض الإعلان
                showSystemUI()

                // Proceed with download without reward
                proceedWithoutRewardedAd()
            }

            override fun onAdShowedFullScreenContent() {
                Log.d("MainActivity", "Rewarded Ad showed fullscreen content.")
                // Ad is now showing, mRewardedAd will be null after this
                mRewardedAd = null
            }
        }

        // Show the ad and define the reward listener
        mRewardedAd?.show(this, OnUserEarnedRewardListener { rewardItem ->
            val rewardAmount = rewardItem.amount
            val rewardType = rewardItem.type
            Log.i("MainActivity", "User earned reward: Amount=$rewardAmount, Type=$rewardType")

            // بدء تحميل المود فور الحصول على المكافأة
            if (pendingDownloadUrl != null && pendingModId != null && pendingModName != null) {
                Log.i("MainActivity", "Reward earned, initiating download for mod: $pendingModName")
                initiateActualDownload(pendingModId!!, pendingModName!!, pendingDownloadUrl!!)
            } else {
                Log.w("MainActivity", "Reward earned but no pending download info found.")
            }
        })
    }


    // Changed function name and implementation for RewardedAd
    private fun loadRewardedAd() {
        // Uses currentRewardedAdUnitId and isRewardedAdEnabled which are now set with defaults in onCreate
        if (!isRewardedAdEnabled || currentRewardedAdUnitId.isBlank()) {
            Log.w("MainActivity", "Rewarded ad loading skipped: disabled or no Unit ID (using defaults).")
            mRewardedAd = null
            return
        }
        Log.d("MainActivity", "Loading Rewarded Ad with Unit ID: $currentRewardedAdUnitId")
        val adRequest = AdRequest.Builder().build()
        RewardedAd.load(this, currentRewardedAdUnitId, adRequest, object : RewardedAdLoadCallback() {
            override fun onAdFailedToLoad(adError: LoadAdError) {
                Log.e("MainActivity", "RewardedAd failed to load: ${adError.message} (Unit ID: $currentRewardedAdUnitId)")
                mRewardedAd = null
            }

            override fun onAdLoaded(rewardedAd: RewardedAd) {
                Log.i("MainActivity", "RewardedAd loaded successfully. (Unit ID: $currentRewardedAdUnitId)")
                mRewardedAd = rewardedAd
                
                // تعيين إعدادات الإعلان المكافئ لضمان تغطية الشاشة بالكامل
                mRewardedAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
                    override fun onAdShowedFullScreenContent() {
                        Log.d("MainActivity", "Rewarded Ad showed fullscreen content.")
                        // إخفاء شريط الحالة وشريط التنقل عند عرض الإعلان
                        hideSystemUI()
                    }
                    
                    override fun onAdDismissedFullScreenContent() {
                        // إعادة عرض شريط الحالة وشريط التنقل بعد إغلاق الإعلان
                        showSystemUI()
                    }
                }
            }
        })
    }
    // --- End AdMob ---

    // وظيفة لإخفاء واجهة النظام (شريط الحالة وشريط التنقل) للعرض بملء الشاشة
    private fun hideSystemUI() {
        try {
            // إخفاء شريط الإشعارات (Status Bar)
            window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

            // إخفاء شريط التنقل (Navigation Bar)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // للإصدارات الأحدث من أندرويد (Android 11+)
                window.setDecorFitsSystemWindows(false)
                val controller = WindowCompat.getInsetsController(window, window.decorView)
                controller.hide(WindowInsetsCompat.Type.systemBars())
                controller.hide(WindowInsetsCompat.Type.statusBars())
                controller.hide(WindowInsetsCompat.Type.navigationBars())
                controller.hide(WindowInsetsCompat.Type.displayCutout())
                controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE

                // إزالة جميع الحشوات لضمان العرض بملء الشاشة
                val rootView = window.decorView.findViewById<ViewGroup>(android.R.id.content)
                WindowCompat.setDecorFitsSystemWindows(window, false)
                ViewCompat.setOnApplyWindowInsetsListener(rootView) { view, windowInsets ->
                    view.setPadding(0, 0, 0, 0)
                    WindowInsetsCompat.CONSUMED
                }
            } else {
                // للإصدارات الأقدم من أندرويد
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LOW_PROFILE
                )
            }

            // تعيين العرض بملء الشاشة
            window.decorView.fitsSystemWindows = false

            // إضافة خصائص إضافية لضمان العرض بملء الشاشة
            window.statusBarColor = android.graphics.Color.TRANSPARENT
            window.navigationBarColor = android.graphics.Color.TRANSPARENT

            Log.d("MainActivity", "System UI hidden for fullscreen experience")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error hiding system UI", e)
        }
    }

    // وظيفة لإظهار واجهة النظام (شريط الحالة وشريط التنقل)
    private fun showSystemUI() {
        try {
            // إعادة عرض شريط الإشعارات (Status Bar)
            window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // للإصدارات الأحدث من أندرويد (Android 11+)
                window.setDecorFitsSystemWindows(true)
                val controller = WindowCompat.getInsetsController(window, window.decorView)
                controller.show(WindowInsetsCompat.Type.systemBars())
                controller.show(WindowInsetsCompat.Type.statusBars())
                controller.show(WindowInsetsCompat.Type.navigationBars())

                // إعادة تعيين الحشوات (padding) للعرض الطبيعي
                val rootView = window.decorView.findViewById<ViewGroup>(android.R.id.content)
                WindowCompat.setDecorFitsSystemWindows(window, true)
                ViewCompat.setOnApplyWindowInsetsListener(rootView) { view, windowInsets ->
                    val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
                    view.setPadding(insets.left, insets.top, insets.right, insets.bottom)
                    windowInsets
                }
            } else {
                // للإصدارات الأقدم من أندرويد
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
            }

            // إعادة تعيين العرض العادي
            window.decorView.fitsSystemWindows = true

            // إعادة تعيين ألوان أشرطة النظام
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.statusBarColor = resources.getColor(android.R.color.black, null)
                window.navigationBarColor = resources.getColor(android.R.color.black, null)
            }

            Log.d("MainActivity", "System UI restored")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error restoring system UI", e)
        }
    }



    // --- REMOVED: ScrollListener Implementation ---

    override fun onResume() {
        super.onResume()
        // Re-apply immersive mode when the activity resumes
        hideSystemUI()
        // REMOVED: Receiver registration
    }

    override fun onPause() {
        super.onPause()
       // REMOVED: Receiver unregistration
    }

    override fun onDestroy() {
        super.onDestroy()
        // Stop the auto-cleanup service
        cleanupHandler.removeCallbacks(cleanupRunnable)
        Log.d("MainActivity", "Auto-cleanup service stopped")
    }

    // --- SharedPreferences Helper Method ---
    private fun saveCompletedDownloadPath(context: Context?, modId: String, filePath: String) {
        // Add null check for context
        if (context == null) {
            Log.e("MainActivity", "Context is null in saveCompletedDownloadPath")
            return
        }
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val pendingJsonString = prefs.getString(PENDING_DOWNLOADS_KEY, "{}") ?: "{}"
        try {
            val pendingDownloads = JSONObject(pendingJsonString)
            pendingDownloads.put(modId, filePath)
            prefs.edit().putString(PENDING_DOWNLOADS_KEY, pendingDownloads.toString()).apply()
            Log.d("MainActivity", "Saved completed download path to SharedPreferences: $modId -> $filePath")
        } catch (e: org.json.JSONException) {
            Log.e("MainActivity", "Error saving completed download path to SharedPreferences", e)
        }
    }


    // Function to create cache directories
    private fun createCacheDirectories() {
        try {
            // Create WebView cache directories
            val webViewCacheDir = File(cacheDir, "WebView")
            if (!webViewCacheDir.exists()) {
                webViewCacheDir.mkdirs()
                Log.d("MainActivity", "Created WebView cache directory")
            }

            val defaultDir = File(webViewCacheDir, "Default")
            if (!defaultDir.exists()) {
                defaultDir.mkdirs()
                Log.d("MainActivity", "Created Default directory")
            }

            val httpCacheDir = File(defaultDir, "HTTP Cache")
            if (!httpCacheDir.exists()) {
                httpCacheDir.mkdirs()
                Log.d("MainActivity", "Created HTTP Cache directory")
            }

            val codeCacheDir = File(httpCacheDir, "Code Cache")
            if (!codeCacheDir.exists()) {
                codeCacheDir.mkdirs()
                Log.d("MainActivity", "Created Code Cache directory")
            }

            val wasmCacheDir = File(codeCacheDir, "wasm")
            if (!wasmCacheDir.exists()) {
                wasmCacheDir.mkdirs()
                Log.d("MainActivity", "Created wasm cache directory")
            }

            // Set permissions
            webViewCacheDir.setReadable(true, false)
            webViewCacheDir.setWritable(true, false)
            defaultDir.setReadable(true, false)
            defaultDir.setWritable(true, false)
            httpCacheDir.setReadable(true, false)
            httpCacheDir.setWritable(true, false)
            codeCacheDir.setReadable(true, false)
            codeCacheDir.setWritable(true, false)
            wasmCacheDir.setReadable(true, false)
            wasmCacheDir.setWritable(true, false)

            Log.d("MainActivity", "Cache directories created and permissions set")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error creating cache directories", e)
        }
    }

    // --- Function to initiate the actual download (Refactored from WebAppInterface) ---
    private fun initiateActualDownload(modId: String, modName: String, downloadUrl: String) {
        Log.d("MainActivity", "Initiating actual download: ID=$modId, Name=$modName, URL=$downloadUrl")

        // Validation and filename generation (moved from original startModDownload)
        if (!downloadUrl.startsWith("http")) {
            runOnUiThread { Toast.makeText(this, "رابط التحميل غير صالح.", Toast.LENGTH_SHORT).show() }
            Log.e("MainActivity", "Invalid download URL: $downloadUrl")
            return
        }

        // Use webAppInterface instance to call the helper function
        val filename = webAppInterface.generateFilenameFromUrl(modName, downloadUrl)
        if (filename == null) {
            runOnUiThread { Toast.makeText(this, "لا يمكن تحديد اسم الملف من الرابط.", Toast.LENGTH_SHORT).show() }
            Log.e("MainActivity", "Could not determine filename for URL: $downloadUrl")
            return
        }
        Log.d("MainActivity", "Determined filename for download: $filename")

        // Determine appropriate MIME type based on file extension
        val mimeType = when {
            filename.endsWith(".mcpack", ignoreCase = true) -> "application/mcpack"
            filename.endsWith(".mcaddon", ignoreCase = true) -> "application/mcaddon"
            filename.endsWith(".zip", ignoreCase = true) -> {
                // Para archivos ZIP, usamos application/zip para la descarga
                // y luego determinaremos el tipo MIME adecuado al abrir el archivo
                "application/zip"
            }
            else -> "application/octet-stream" // Default fallback
        }
        Log.d("MainActivity", "Using MIME type for download: $mimeType")

        // Download logic (moved from original startModDownload)
        try {
            val request = DownloadManager.Request(Uri.parse(downloadUrl))
            request.setTitle(filename) // Use the non-null filename directly
            request.setDescription("جارٍ تحميل المود...")
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
            // Explicitly allow both Wi-Fi and Mobile networks
            request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI or DownloadManager.Request.NETWORK_MOBILE)
            request.setAllowedOverMetered(true) // Keep this for clarity, though implied by NETWORK_MOBILE
            request.setAllowedOverRoaming(true) // Allow download while roaming
            // --- Use App-Specific External Downloads directory ---
            request.setDestinationInExternalFilesDir(this@MainActivity, Environment.DIRECTORY_DOWNLOADS, filename) // Use filename directly (already checked non-null)
            // --- END CHANGE ---
            request.setMimeType(mimeType) // Use the determined MIME type

            val downloadId = downloadManager.enqueue(request)
            Log.i("MainActivity", "Enqueued download with ID: $downloadId, Filename: $filename, MIME type: $mimeType, Destination: App's External Downloads") // Updated log message

            activeDownloads[downloadId] = modId
            Log.d("MainActivity", "Tracking download ID $downloadId for mod ID $modId.")

            webAppInterface.scheduleDownloadCheck(downloadId, modId) // Use helper from interface instance

            runOnUiThread {
                val message = "بدء تحميل $filename..."
                Toast.makeText(this, message, Toast.LENGTH_LONG).show()
            }

        } catch (e: Exception) {
            Log.e("MainActivity", "Error setting up or enqueuing download for $modName", e)
            runOnUiThread { Toast.makeText(this, "حدث خطأ أثناء بدء التحميل.", Toast.LENGTH_SHORT).show() }
        }
    }
    // --- End initiateActualDownload ---


    // JavaScript Interface Class
    inner class WebAppInterface(private val context: Context) {

        // Function to get app version name for JavaScript
        @JavascriptInterface
        fun getAppVersionName(): String {
            return try {
                val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
                packageInfo.versionName ?: "unknown" // Use Elvis operator to handle null case
            } catch (e: Exception) {
                Log.e("WebAppInterface", "Error getting app version name", e)
                "unknown" // Fallback value if version can't be determined
            }
        }

        // Safe clipboard access function
        @JavascriptInterface
        fun copyToClipboard(text: String): Boolean {
            return try {
                // Only attempt to copy when app is in foreground
                runOnUiThread {
                    try {
                        val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                        val clipData = android.content.ClipData.newPlainText("Modetaris Text", text)
                        clipboardManager.setPrimaryClip(clipData)
                        Toast.makeText(context, "تم النسخ إلى الحافظة", Toast.LENGTH_SHORT).show()
                    } catch (e: Exception) {
                        Log.e("WebAppInterface", "Error copying to clipboard", e)
                        Toast.makeText(context, "فشل النسخ إلى الحافظة", Toast.LENGTH_SHORT).show()
                    }
                }
                true
            } catch (e: Exception) {
                Log.e("WebAppInterface", "Error in copyToClipboard", e)
                false
            }
        }

        // *** NEW Function: Called by JS to request download WITH AD ***
        @JavascriptInterface
        fun requestModDownloadWithAd(modId: String, modName: String, downloadUrl: String) {
             Log.i("WebAppInterface", "requestModDownloadWithAd called for mod: $modName")
             runOnUiThread { // Ensure UI operations are on the main thread
                 // Store download info FIRST
                 pendingModId = modId
                 pendingModName = modName
                 pendingDownloadUrl = downloadUrl
                 Log.d("WebAppInterface", "Stored pending download info: ID=$modId, Name=$modName, URL=$downloadUrl")

                 // No longer need to check/request permission for app-specific storage on API 23+
                 Log.d("WebAppInterface", "Proceeding directly to ad/download flow (no permission check needed).")
                 showAdOrDownload() // Proceed directly
             }
        }

        // Helper function within WebAppInterface to avoid duplicating ad logic
        internal fun showAdOrDownload() {
            Log.d("WebAppInterface", "showAdOrDownload() called, isRewardedAdEnabled: $isRewardedAdEnabled")

            if (!isRewardedAdEnabled) {
                Log.i("WebAppInterface", "Rewarded ad is disabled by config. Proceeding directly with download.")
                if (<EMAIL> != null && <EMAIL> != null && <EMAIL> != null) {
                    <EMAIL>(<EMAIL>!!, <EMAIL>!!, <EMAIL>!!)
                }
                <EMAIL>()
                // Optionally, still try to load the next ad if it was just temporarily unavailable but now enabled
                if(isRewardedAdEnabled) loadRewardedAd()
                return
            }

            // Show consent dialog first (Google AdMob policy compliance)
            Log.i("WebAppInterface", "Showing consent dialog for rewarded ad")
            Log.d("WebAppInterface", "About to call showConsentDialog()")
            <EMAIL>()
            Log.d("WebAppInterface", "showConsentDialog() call completed")
        }
        // Helper function to generate filename from URL and mod name
        fun generateFilenameFromUrl(fallbackName: String, url: String): String? {
            var originalFileName: String? = null
            var fileExtension: String = ".mcaddon" // Default extension

            try {
                val decodedUrl = URLDecoder.decode(url, "UTF-8")
                val pathPart = decodedUrl.substringBefore("?")
                val lastSlashIndex = pathPart.lastIndexOf('/')
                if (lastSlashIndex != -1 && lastSlashIndex < pathPart.length - 1) {
                    originalFileName = pathPart.substring(lastSlashIndex + 1)
                    Log.d("WebAppInterface", "Extracted original filename from URL: $originalFileName")

                    // Determine file extension based on the original filename
                    if (originalFileName!!.endsWith(".mcaddon", ignoreCase = true)) {
                        fileExtension = ".mcaddon"
                        Log.d("WebAppInterface", "MCADDON file detected, keeping .mcaddon extension")
                    } else if (originalFileName!!.endsWith(".mcpack", ignoreCase = true)) {
                        fileExtension = ".mcpack"
                        Log.d("WebAppInterface", "MCPACK file detected, keeping .mcpack extension")
                    } else if (originalFileName!!.endsWith(".zip", ignoreCase = true)) {
                        fileExtension = ".zip"
                        Log.d("WebAppInterface", "ZIP file detected, keeping .zip extension")
                    }
                    // If none of the above, it will use the default .mcaddon
                }
            } catch (e: Exception) {
                Log.e("WebAppInterface", "Error decoding/parsing URL for filename: $url", e)
                // Fallback to default extension if URL parsing fails
            }

            var baseName: String?

            // Use original filename if available, otherwise use fallback name
            baseName = if (originalFileName != null && originalFileName!!.isNotEmpty()) {
                // Remove the extension from the original filename to get the base name
                val dotIndex = originalFileName!!.lastIndexOf('.')
                if (dotIndex > 0) {
                    originalFileName!!.substring(0, dotIndex)
                } else {
                    originalFileName!!
                }
            } else {
                fallbackName
            }

            // Clean the base name to ensure it's safe for filesystem
            baseName = baseName.replace(Regex("[^a-zA-Z0-9._\\-\\s]"), "_")
                .replace(Regex("\\s+"), "_")
                .trim('_')

            // Ensure the base name is not empty
            if (baseName.isEmpty()) {
                baseName = "mod_file"
            }

            val finalFilename = "$baseName$fileExtension"
            Log.d("WebAppInterface", "Generated filename: $finalFilename (base: $baseName, extension: $fileExtension)")
            return finalFilename
        }

        fun scheduleDownloadCheck(downloadId: Long, modId: String) {
            pollingHandler.postDelayed(object : Runnable {
                override fun run() {
                    checkDownloadStatus(downloadId, modId)
                }
            }, 2000) // Check after 2 seconds
        }

        private fun checkDownloadStatus(downloadId: Long, modId: String) {
            val query = DownloadManager.Query().setFilterById(downloadId)
            val cursor = downloadManager.query(query)
            if (cursor != null && cursor.moveToFirst()) {
                val statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)
                if (statusIndex != -1) {
                    val status = cursor.getInt(statusIndex)
                    when (status) {
                        DownloadManager.STATUS_SUCCESSFUL -> {
                            Log.d("WebAppInterface", "Download $downloadId successful for mod $modId")
                            activeDownloads.remove(downloadId)
                            val uriIndex = cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)
                            if (uriIndex != -1) {
                                val uriString = cursor.getString(uriIndex)
                                saveCompletedDownloadPath(context, modId, uriString)
                            }
                        }
                        DownloadManager.STATUS_FAILED -> {
                            Log.e("WebAppInterface", "Download $downloadId failed for mod $modId")
                            activeDownloads.remove(downloadId)
                        }
                        DownloadManager.STATUS_PENDING, DownloadManager.STATUS_RUNNING -> {
                            Log.d("WebAppInterface", "Download $downloadId still in progress for mod $modId")
                            scheduleDownloadCheck(downloadId, modId) // Reschedule
                        }
                        else -> {
                             activeDownloads.remove(downloadId)
                        }
                    }
                }
            }
            cursor?.close()
        }
    } // End of WebAppInterface class

    // Function to clear pending download info from SharedPreferences
    private fun clearPendingDownloadInfo() {
        val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().remove(PENDING_DOWNLOADS_KEY).apply()
        Log.d("MainActivity", "Cleared pending download info.")
    }

    // Move onBackPressed back inside MainActivity class
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        Log.d("MainActivity", "Back button pressed - checking for open modals")

        // Check if a mod details modal is open in the WebView with improved detection
        webView.evaluateJavascript(
            "(function() { " +
            "    try { " +
            "        // Check main modal" +
            "        var modal = document.getElementById('modal'); " +
            "        if(modal && modal.style && (modal.style.display === 'flex' || modal.style.display === 'block' || window.getComputedStyle(modal).display !== 'none')) { " +
            "            return 'modal'; " +
            "        } " +
            "        // Check creator info modal" +
            "        var creatorModal = document.querySelector('.creator-info-modal'); " +
            "        if(creatorModal && window.getComputedStyle(creatorModal).display !== 'none') { " +
            "            return 'creator-modal'; " +
            "        } " +
            "        // Check custom dialog overlay" +
            "        var customDialog = document.querySelector('.custom-dialog-overlay'); " +
            "        if(customDialog && window.getComputedStyle(customDialog).display !== 'none') { " +
            "            return 'custom-dialog'; " +
            "        } " +
            "        // Check any other modal-like elements" +
            "        var anyModal = document.querySelector('.modal, [id*=\"modal\"], [class*=\"overlay\"]'); " +
            "        if(anyModal && window.getComputedStyle(anyModal).display !== 'none') { " +
            "            return 'other-modal'; " +
            "        } " +
            "        return 'none'; " +
            "    } catch(e) { " +
            "        console.error('Error checking modals:', e); " +
            "        return 'error'; " +
            "    } " +
            "})()"
        ) { result ->
            Log.d("MainActivity", "Modal check result: $result")
            when (result) {
                "modal", "creator-modal", "custom-dialog", "other-modal" -> {
                    Log.d("MainActivity", "Modal detected, attempting to close")
                    closeAllModals()
                }
                "none" -> {
                    Log.d("MainActivity", "No modals detected, checking WebView navigation")
                    // Check if WebView can go back
                    if (webView.canGoBack()) {
                        Log.d("MainActivity", "WebView can go back, navigating back")
                        webView.goBack()
                    } else {
                        Log.d("MainActivity", "WebView cannot go back, calling super.onBackPressed()")
                        super.onBackPressed()
                    }
                }
                else -> {
                    Log.d("MainActivity", "Error or unknown result, defaulting to WebView back check")
                    if (webView.canGoBack()) {
                        webView.goBack()
                    } else {
                        super.onBackPressed()
                    }
                }
            }
        }
    }

    // وظيفة مساعدة لإغلاق جميع النوافذ المفتوحة
    private fun closeAllModals() {
        Log.d("MainActivity", "Attempting to close all modals")
        webView.evaluateJavascript(
            "(function() { " +
            "    try { " +
            "        // إغلاق النافذة الرئيسية للمود" +
            "        if(typeof closeModal === 'function') { closeModal(); } " +
            "        // إغلاق جميع النوافذ المرئية" +
            "        var modals = document.querySelectorAll('.modal, [id*=\"modal\"], [class*=\"overlay\"], .creator-info-modal, .custom-dialog-overlay'); " +
            "        modals.forEach(function(modal) { " +
            "            modal.style.display = 'none'; " +
            "            modal.style.visibility = 'hidden'; " +
            "            modal.style.opacity = '0'; " +
            "        }); " +
            "        console.log('All modals closed'); " +
            "        return 'success'; " +
            "    } catch(e) { " +
            "        console.error('Error closing modals:', e); " +
            "        return 'error'; " +
            "    } " +
            "})()", null
        )
    }

    // Auto-cleanup functionality for old downloads
    private fun performCleanupOfOldDownloads() {
        Log.d("MainActivity", "Starting cleanup of old downloads")

        val isAutoCleanupEnabled = sharedPreferences.getBoolean(AUTO_DELETE_ENABLED_KEY, true)
        // Check if cleanup is enabled
        if (!isAutoCleanupEnabled) {
            Log.d("MainActivity", "Auto-cleanup is disabled, skipping")
            // Schedule next check anyway
            scheduleNextCleanup()
            return
        }

        try {
            // Get the download timestamps from SharedPreferences
            val timestampsJson = sharedPreferences.getString(MOD_DOWNLOAD_TIMESTAMPS_KEY, "{}") ?: "{}"
            val timestamps = JSONObject(timestampsJson)
            val currentTime = System.currentTimeMillis()
            val keysToRemove = mutableListOf<String>()

            // Check each mod's download timestamp
            val keys = timestamps.keys()
            while (keys.hasNext()) {
                val modId = keys.next()
                val downloadTime = timestamps.getLong(modId)
                val ageInMillis = currentTime - downloadTime

                // If the file is older than the cleanup interval, mark for deletion
                if (ageInMillis > AUTO_CLEANUP_INTERVAL_MS) {
                    Log.d("MainActivity", "Mod $modId is old (${ageInMillis / 1000 / 60} minutes), marking for cleanup")
                    keysToRemove.add(modId)
                }
            }

            // Delete old files and remove their timestamps
            if (keysToRemove.isNotEmpty()) {
                val downloadsDir = File(getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "")
                var deletedCount = 0

                for (modId in keysToRemove) {
                    // Try to find and delete the file
                    val files = downloadsDir.listFiles { file ->
                        file.name.contains(modId, ignoreCase = true) ||
                        file.name.startsWith(modId, ignoreCase = true)
                    }

                    files?.forEach { file ->
                        if (file.delete()) {
                            Log.d("MainActivity", "Deleted old download: ${file.name}")
                            deletedCount++
                        } else {
                            Log.w("MainActivity", "Failed to delete old download: ${file.name}")
                        }
                    }

                    // Remove the timestamp entry
                    timestamps.remove(modId)
                }

                // Save updated timestamps
                sharedPreferences.edit().putString(MOD_DOWNLOAD_TIMESTAMPS_KEY, timestamps.toString()).apply()

                if (deletedCount > 0) {
                    Log.d("MainActivity", "Cleanup completed: deleted $deletedCount old files")
                    // Show notification to user about deleted files
                    showAutoDeleteNotification(keysToRemove.size)
                } else {
                    Log.d("MainActivity", "No old downloads to clean up")
                }
            } else {
                Log.d("MainActivity", "No old downloads to clean up")
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error during cleanup of old downloads", e)
        }

        // Schedule next cleanup
        scheduleNextCleanup()
    }

    // Schedule next cleanup
    private fun scheduleNextCleanup() {
        cleanupHandler.removeCallbacks(cleanupRunnable)
        cleanupHandler.postDelayed(cleanupRunnable, AUTO_CLEANUP_INTERVAL_MS)
        Log.d("MainActivity", "Scheduled next cleanup in ${AUTO_CLEANUP_INTERVAL_MS / 1000 / 60} minutes")
    }

    // Show notification about auto-deleted files
    private fun showAutoDeleteNotification(filesCount: Int) {
        try {
            // Create notification channel for Android 8.0+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channelId = "auto_delete_channel"
                val channelName = "Auto Delete Notifications"
                val importance = NotificationManager.IMPORTANCE_DEFAULT
                val channel = NotificationChannel(channelId, channelName, importance)
                val notificationManager = getSystemService(NotificationManager::class.java)
                notificationManager.createNotificationChannel(channel)
            }

            val notificationBuilder = NotificationCompat.Builder(this, "auto_delete_channel")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle("تنظيف تلقائي")
                .setContentText("تم حذف $filesCount ملف قديم لتوفير مساحة التخزين")
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setAutoCancel(true)

            val notificationManager = NotificationManagerCompat.from(this)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED) {
                    notificationManager.notify(1001, notificationBuilder.build())
                } else {
                    Log.d("MainActivity", "Cannot show auto-delete notification: No notification permission")
                }
            } else {
                notificationManager.notify(1001, notificationBuilder.build())
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error showing auto-delete notification", e)
        }
    }


} // End of MainActivity class
