  Manifest android  POST_NOTIFICATIONS android.Manifest.permission  black android.R.color  ic_dialog_info android.R.drawable  content android.R.id  SuppressLint android.annotation  Activity android.app  DownloadManager android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  AUTO_DELETE_DELAY_MS android.app.Activity  AdError android.app.Activity  	AdRequest android.app.Activity  Boolean android.app.Activity  Build android.app.Activity  ConcurrentHashMap android.app.Activity  Context android.app.Activity  
ContextCompat android.app.Activity  CountDownTimer android.app.Activity  DEFAULT_BANNER_AD_UNIT_ID android.app.Activity  DEFAULT_REWARDED_AD_UNIT_ID android.app.Activity  DownloadManager android.app.Activity  Environment android.app.Activity  	Exception android.app.Activity  File android.app.Activity  FullScreenContentCallback android.app.Activity  Handler android.app.Activity  Intent android.app.Activity  
JSONObject android.app.Activity  LoadAdError android.app.Activity  Log android.app.Activity  Long android.app.Activity  Looper android.app.Activity  	MobileAds android.app.Activity  NotificationChannel android.app.Activity  NotificationCompat android.app.Activity  NotificationManager android.app.Activity  NotificationManagerCompat android.app.Activity  OnUserEarnedRewardListener android.app.Activity  PackageManager android.app.Activity  R android.app.Activity  Regex android.app.Activity  RelativeLayout android.app.Activity  
RewardedAd android.app.Activity  RewardedAdLoadCallback android.app.Activity  Runnable android.app.Activity  String android.app.Activity  Suppress android.app.Activity  System android.app.Activity  Toast android.app.Activity  
URLDecoder android.app.Activity  Uri android.app.Activity  View android.app.Activity  
ViewCompat android.app.Activity  	ViewGroup android.app.Activity  WebAppInterface android.app.Activity  WebSettings android.app.Activity  WebView android.app.Activity  
WebViewClient android.app.Activity  WindowCompat android.app.Activity  WindowInsetsCompat android.app.Activity  WindowInsetsControllerCompat android.app.Activity  
WindowManager android.app.Activity  activeDownloads android.app.Activity  adViewBanner android.app.Activity  android android.app.Activity  bannerAdPosition android.app.Activity  btnAcceptRewardedAd android.app.Activity  btnDeclineRewardedAd android.app.Activity  checkDownloadStatus android.app.Activity  clearPendingDownloadInfo android.app.Activity  contains android.app.Activity  createCacheDirectories android.app.Activity  currentBannerAdUnitId android.app.Activity  currentRewardedAdUnitId android.app.Activity  downloadManager android.app.Activity  endsWith android.app.Activity  findViewById android.app.Activity  forEach android.app.Activity  getSharedPreferences android.app.Activity  getSystemService android.app.Activity  hideConsentDialog android.app.Activity  hideSystemUI android.app.Activity  installSplashScreen android.app.Activity  isBannerAdEnabled android.app.Activity  isBlank android.app.Activity  isEmpty android.app.Activity  isFirstTouchEventAfterLoad android.app.Activity  
isNotEmpty android.app.Activity  isRewardedAdEnabled android.app.Activity  java android.app.Activity  lastIndexOf android.app.Activity  launch android.app.Activity  lifecycleScope android.app.Activity  loadRewardedAd android.app.Activity  mRewardedAd android.app.Activity  
mutableListOf android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  onPause android.app.Activity  onResume android.app.Activity  org android.app.Activity  pendingDownloadUrl android.app.Activity  pendingModId android.app.Activity  pendingModName android.app.Activity  pollingHandler android.app.Activity  proceedWithoutRewardedAd android.app.Activity  replace android.app.Activity  rewardedAdConsentOverlay android.app.Activity  
runOnUiThread android.app.Activity  saveCompletedDownloadPath android.app.Activity  scheduleNextCleanup android.app.Activity  set android.app.Activity  setContentView android.app.Activity  setupConsentDialogListeners android.app.Activity  sharedPreferences android.app.Activity  showSystemUI android.app.Activity  
startActivity android.app.Activity  
startsWith android.app.Activity  	substring android.app.Activity  substringBefore android.app.Activity  trim android.app.Activity  tvConsentTimer android.app.Activity  webAppInterface android.app.Activity  webView android.app.Activity  window android.app.Activity  LayoutParams #android.app.Activity.RelativeLayout  content android.app.Activity.android  ClipboardManager $android.app.Activity.android.content  json android.app.Activity.org  
JSONException android.app.Activity.org.json  ACTION_DOWNLOAD_COMPLETE android.app.DownloadManager  COLUMN_LOCAL_FILENAME android.app.DownloadManager  COLUMN_LOCAL_URI android.app.DownloadManager  
COLUMN_REASON android.app.DownloadManager  
COLUMN_STATUS android.app.DownloadManager  COLUMN_TITLE android.app.DownloadManager  EXTRA_DOWNLOAD_ID android.app.DownloadManager  Query android.app.DownloadManager  Request android.app.DownloadManager  
STATUS_FAILED android.app.DownloadManager  STATUS_PENDING android.app.DownloadManager  STATUS_RUNNING android.app.DownloadManager  STATUS_SUCCESSFUL android.app.DownloadManager  enqueue android.app.DownloadManager  query android.app.DownloadManager  
setFilterById !android.app.DownloadManager.Query  NETWORK_MOBILE #android.app.DownloadManager.Request  NETWORK_WIFI #android.app.DownloadManager.Request  #VISIBILITY_VISIBLE_NOTIFY_COMPLETED #android.app.DownloadManager.Request  setAllowedNetworkTypes #android.app.DownloadManager.Request  setAllowedOverMetered #android.app.DownloadManager.Request  setAllowedOverRoaming #android.app.DownloadManager.Request  setDescription #android.app.DownloadManager.Request   setDestinationInExternalFilesDir #android.app.DownloadManager.Request  setMimeType #android.app.DownloadManager.Request  setNotificationVisibility #android.app.DownloadManager.Request  setTitle #android.app.DownloadManager.Request  IMPORTANCE_DEFAULT android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  BroadcastReceiver android.content  ClipboardManager android.content  ContentProvider android.content  
ContentValues android.content  Context android.content  Intent android.content  IntentFilter android.content  SharedPreferences android.content  ACTION_DOWNLOAD_STATUS_UPDATE !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  DownloadManager !android.content.BroadcastReceiver  EXTRA_DOWNLOAD_ID !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  
JSONObject !android.content.BroadcastReceiver  LocalBroadcastManager !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  PENDING_DOWNLOADS_KEY !android.content.BroadcastReceiver  
PREFS_NAME !android.content.BroadcastReceiver  apply !android.content.BroadcastReceiver  getModIdForDownload !android.content.BroadcastReceiver  org !android.content.BroadcastReceiver  removeDownloadMapping !android.content.BroadcastReceiver  saveCompletedDownloadPath !android.content.BroadcastReceiver  json %android.content.BroadcastReceiver.org  
JSONException *android.content.BroadcastReceiver.org.json  newPlainText android.content.ClipData  setPrimaryClip  android.content.ClipboardManager  Log android.content.ContentProvider  AUTO_DELETE_DELAY_MS android.content.Context  AdError android.content.Context  	AdRequest android.content.Context  Boolean android.content.Context  Build android.content.Context  CLIPBOARD_SERVICE android.content.Context  ConcurrentHashMap android.content.Context  Context android.content.Context  
ContextCompat android.content.Context  CountDownTimer android.content.Context  DEFAULT_BANNER_AD_UNIT_ID android.content.Context  DEFAULT_REWARDED_AD_UNIT_ID android.content.Context  DOWNLOAD_SERVICE android.content.Context  DownloadManager android.content.Context  Environment android.content.Context  	Exception android.content.Context  File android.content.Context  FullScreenContentCallback android.content.Context  Handler android.content.Context  Intent android.content.Context  
JSONObject android.content.Context  LoadAdError android.content.Context  Log android.content.Context  Long android.content.Context  Looper android.content.Context  MODE_PRIVATE android.content.Context  	MobileAds android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  NotificationManagerCompat android.content.Context  OnUserEarnedRewardListener android.content.Context  PackageManager android.content.Context  R android.content.Context  Regex android.content.Context  RelativeLayout android.content.Context  
RewardedAd android.content.Context  RewardedAdLoadCallback android.content.Context  Runnable android.content.Context  String android.content.Context  Suppress android.content.Context  System android.content.Context  Toast android.content.Context  
URLDecoder android.content.Context  Uri android.content.Context  View android.content.Context  
ViewCompat android.content.Context  	ViewGroup android.content.Context  WebAppInterface android.content.Context  WebSettings android.content.Context  WebView android.content.Context  
WebViewClient android.content.Context  WindowCompat android.content.Context  WindowInsetsCompat android.content.Context  WindowInsetsControllerCompat android.content.Context  
WindowManager android.content.Context  activeDownloads android.content.Context  adViewBanner android.content.Context  android android.content.Context  bannerAdPosition android.content.Context  btnAcceptRewardedAd android.content.Context  btnDeclineRewardedAd android.content.Context  checkDownloadStatus android.content.Context  clearPendingDownloadInfo android.content.Context  contains android.content.Context  createCacheDirectories android.content.Context  currentBannerAdUnitId android.content.Context  currentRewardedAdUnitId android.content.Context  downloadManager android.content.Context  endsWith android.content.Context  findViewById android.content.Context  forEach android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  hideConsentDialog android.content.Context  hideSystemUI android.content.Context  installSplashScreen android.content.Context  isBannerAdEnabled android.content.Context  isBlank android.content.Context  isEmpty android.content.Context  isFirstTouchEventAfterLoad android.content.Context  
isNotEmpty android.content.Context  isRewardedAdEnabled android.content.Context  java android.content.Context  lastIndexOf android.content.Context  launch android.content.Context  lifecycleScope android.content.Context  loadRewardedAd android.content.Context  mRewardedAd android.content.Context  
mutableListOf android.content.Context  org android.content.Context  packageManager android.content.Context  packageName android.content.Context  pendingDownloadUrl android.content.Context  pendingModId android.content.Context  pendingModName android.content.Context  pollingHandler android.content.Context  proceedWithoutRewardedAd android.content.Context  replace android.content.Context  rewardedAdConsentOverlay android.content.Context  
runOnUiThread android.content.Context  saveCompletedDownloadPath android.content.Context  scheduleNextCleanup android.content.Context  set android.content.Context  setContentView android.content.Context  setupConsentDialogListeners android.content.Context  sharedPreferences android.content.Context  showSystemUI android.content.Context  
startActivity android.content.Context  
startsWith android.content.Context  	substring android.content.Context  substringBefore android.content.Context  trim android.content.Context  tvConsentTimer android.content.Context  webAppInterface android.content.Context  webView android.content.Context  LayoutParams &android.content.Context.RelativeLayout  content android.content.Context.android  ClipboardManager 'android.content.Context.android.content  json android.content.Context.org  
JSONException  android.content.Context.org.json  AUTO_DELETE_DELAY_MS android.content.ContextWrapper  AdError android.content.ContextWrapper  	AdRequest android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  ConcurrentHashMap android.content.ContextWrapper  Context android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  CountDownTimer android.content.ContextWrapper  DEFAULT_BANNER_AD_UNIT_ID android.content.ContextWrapper  DEFAULT_REWARDED_AD_UNIT_ID android.content.ContextWrapper  DownloadManager android.content.ContextWrapper  Environment android.content.ContextWrapper  	Exception android.content.ContextWrapper  File android.content.ContextWrapper  FullScreenContentCallback android.content.ContextWrapper  Handler android.content.ContextWrapper  Intent android.content.ContextWrapper  
JSONObject android.content.ContextWrapper  LoadAdError android.content.ContextWrapper  Log android.content.ContextWrapper  Long android.content.ContextWrapper  Looper android.content.ContextWrapper  	MobileAds android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  NotificationManagerCompat android.content.ContextWrapper  OnUserEarnedRewardListener android.content.ContextWrapper  PackageManager android.content.ContextWrapper  R android.content.ContextWrapper  Regex android.content.ContextWrapper  RelativeLayout android.content.ContextWrapper  
RewardedAd android.content.ContextWrapper  RewardedAdLoadCallback android.content.ContextWrapper  Runnable android.content.ContextWrapper  String android.content.ContextWrapper  Suppress android.content.ContextWrapper  System android.content.ContextWrapper  Toast android.content.ContextWrapper  
URLDecoder android.content.ContextWrapper  Uri android.content.ContextWrapper  View android.content.ContextWrapper  
ViewCompat android.content.ContextWrapper  	ViewGroup android.content.ContextWrapper  WebAppInterface android.content.ContextWrapper  WebSettings android.content.ContextWrapper  WebView android.content.ContextWrapper  
WebViewClient android.content.ContextWrapper  WindowCompat android.content.ContextWrapper  WindowInsetsCompat android.content.ContextWrapper  WindowInsetsControllerCompat android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  activeDownloads android.content.ContextWrapper  adViewBanner android.content.ContextWrapper  android android.content.ContextWrapper  bannerAdPosition android.content.ContextWrapper  btnAcceptRewardedAd android.content.ContextWrapper  btnDeclineRewardedAd android.content.ContextWrapper  cacheDir android.content.ContextWrapper  checkDownloadStatus android.content.ContextWrapper  clearPendingDownloadInfo android.content.ContextWrapper  contains android.content.ContextWrapper  createCacheDirectories android.content.ContextWrapper  currentBannerAdUnitId android.content.ContextWrapper  currentRewardedAdUnitId android.content.ContextWrapper  downloadManager android.content.ContextWrapper  endsWith android.content.ContextWrapper  findViewById android.content.ContextWrapper  forEach android.content.ContextWrapper  getExternalFilesDir android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  getSystemService android.content.ContextWrapper  hideConsentDialog android.content.ContextWrapper  hideSystemUI android.content.ContextWrapper  installSplashScreen android.content.ContextWrapper  isBannerAdEnabled android.content.ContextWrapper  isBlank android.content.ContextWrapper  isEmpty android.content.ContextWrapper  isFirstTouchEventAfterLoad android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  isRewardedAdEnabled android.content.ContextWrapper  java android.content.ContextWrapper  lastIndexOf android.content.ContextWrapper  launch android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  loadRewardedAd android.content.ContextWrapper  mRewardedAd android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  org android.content.ContextWrapper  pendingDownloadUrl android.content.ContextWrapper  pendingModId android.content.ContextWrapper  pendingModName android.content.ContextWrapper  pollingHandler android.content.ContextWrapper  proceedWithoutRewardedAd android.content.ContextWrapper  replace android.content.ContextWrapper  rewardedAdConsentOverlay android.content.ContextWrapper  
runOnUiThread android.content.ContextWrapper  saveCompletedDownloadPath android.content.ContextWrapper  scheduleNextCleanup android.content.ContextWrapper  set android.content.ContextWrapper  setContentView android.content.ContextWrapper  setupConsentDialogListeners android.content.ContextWrapper  sharedPreferences android.content.ContextWrapper  showSystemUI android.content.ContextWrapper  
startActivity android.content.ContextWrapper  
startsWith android.content.ContextWrapper  	substring android.content.ContextWrapper  substringBefore android.content.ContextWrapper  trim android.content.ContextWrapper  tvConsentTimer android.content.ContextWrapper  webAppInterface android.content.ContextWrapper  webView android.content.ContextWrapper  LayoutParams -android.content.ContextWrapper.RelativeLayout  content &android.content.ContextWrapper.android  ClipboardManager .android.content.ContextWrapper.android.content  json "android.content.ContextWrapper.org  
JSONException 'android.content.ContextWrapper.org.json  
ACTION_SENDTO android.content.Intent  ACTION_VIEW android.content.Intent  EXTRA_DOWNLOAD_ID android.content.Intent  action android.content.Intent  apply android.content.Intent  getLongExtra android.content.Intent  putExtra android.content.Intent  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  remove (android.content.SharedPreferences.Editor  PackageInfo android.content.pm  PackageManager android.content.pm  versionName android.content.pm.PackageInfo  PERMISSION_GRANTED !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  getColor android.content.res.Resources  Cursor android.database  close android.database.Cursor  getColumnIndex android.database.Cursor  getInt android.database.Cursor  	getString android.database.Cursor  moveToFirst android.database.Cursor  Color android.graphics  TRANSPARENT android.graphics.Color  
ColorDrawable android.graphics.drawable  Uri android.net  parse android.net.Uri  scheme android.net.Uri  Build 
android.os  Bundle 
android.os  CountDownTimer 
android.os  Environment 
android.os  Handler 
android.os  Looper 
android.os  SDK_INT android.os.Build.VERSION  LOLLIPOP android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  Log android.os.CountDownTimer  cancel android.os.CountDownTimer  hideConsentDialog android.os.CountDownTimer  proceedWithoutRewardedAd android.os.CountDownTimer  start android.os.CountDownTimer  tvConsentTimer android.os.CountDownTimer  DIRECTORY_DOWNLOADS android.os.Environment  postDelayed android.os.Handler  removeCallbacks android.os.Handler  
getMainLooper android.os.Looper  Settings android.provider  AttributeSet android.util  Log android.util  
TypedValue android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  MotionEvent android.view  View android.view  	ViewGroup android.view  
WindowManager android.view  AUTO_DELETE_DELAY_MS  android.view.ContextThemeWrapper  AdError  android.view.ContextThemeWrapper  	AdRequest  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  ConcurrentHashMap  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  CountDownTimer  android.view.ContextThemeWrapper  DEFAULT_BANNER_AD_UNIT_ID  android.view.ContextThemeWrapper  DEFAULT_REWARDED_AD_UNIT_ID  android.view.ContextThemeWrapper  DownloadManager  android.view.ContextThemeWrapper  Environment  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  File  android.view.ContextThemeWrapper  FullScreenContentCallback  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  
JSONObject  android.view.ContextThemeWrapper  LoadAdError  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Long  android.view.ContextThemeWrapper  Looper  android.view.ContextThemeWrapper  	MobileAds  android.view.ContextThemeWrapper  NotificationChannel  android.view.ContextThemeWrapper  NotificationCompat  android.view.ContextThemeWrapper  NotificationManager  android.view.ContextThemeWrapper  NotificationManagerCompat  android.view.ContextThemeWrapper  OnUserEarnedRewardListener  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  Regex  android.view.ContextThemeWrapper  RelativeLayout  android.view.ContextThemeWrapper  
RewardedAd  android.view.ContextThemeWrapper  RewardedAdLoadCallback  android.view.ContextThemeWrapper  Runnable  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Suppress  android.view.ContextThemeWrapper  System  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  
URLDecoder  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  
ViewCompat  android.view.ContextThemeWrapper  	ViewGroup  android.view.ContextThemeWrapper  WebAppInterface  android.view.ContextThemeWrapper  WebSettings  android.view.ContextThemeWrapper  WebView  android.view.ContextThemeWrapper  
WebViewClient  android.view.ContextThemeWrapper  WindowCompat  android.view.ContextThemeWrapper  WindowInsetsCompat  android.view.ContextThemeWrapper  WindowInsetsControllerCompat  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  activeDownloads  android.view.ContextThemeWrapper  adViewBanner  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  bannerAdPosition  android.view.ContextThemeWrapper  btnAcceptRewardedAd  android.view.ContextThemeWrapper  btnDeclineRewardedAd  android.view.ContextThemeWrapper  checkDownloadStatus  android.view.ContextThemeWrapper  clearPendingDownloadInfo  android.view.ContextThemeWrapper  contains  android.view.ContextThemeWrapper  createCacheDirectories  android.view.ContextThemeWrapper  currentBannerAdUnitId  android.view.ContextThemeWrapper  currentRewardedAdUnitId  android.view.ContextThemeWrapper  downloadManager  android.view.ContextThemeWrapper  endsWith  android.view.ContextThemeWrapper  findViewById  android.view.ContextThemeWrapper  forEach  android.view.ContextThemeWrapper  getSharedPreferences  android.view.ContextThemeWrapper  getSystemService  android.view.ContextThemeWrapper  hideConsentDialog  android.view.ContextThemeWrapper  hideSystemUI  android.view.ContextThemeWrapper  installSplashScreen  android.view.ContextThemeWrapper  isBannerAdEnabled  android.view.ContextThemeWrapper  isBlank  android.view.ContextThemeWrapper  isEmpty  android.view.ContextThemeWrapper  isFirstTouchEventAfterLoad  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  isRewardedAdEnabled  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  lastIndexOf  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  loadRewardedAd  android.view.ContextThemeWrapper  mRewardedAd  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  org  android.view.ContextThemeWrapper  pendingDownloadUrl  android.view.ContextThemeWrapper  pendingModId  android.view.ContextThemeWrapper  pendingModName  android.view.ContextThemeWrapper  pollingHandler  android.view.ContextThemeWrapper  proceedWithoutRewardedAd  android.view.ContextThemeWrapper  replace  android.view.ContextThemeWrapper  	resources  android.view.ContextThemeWrapper  rewardedAdConsentOverlay  android.view.ContextThemeWrapper  
runOnUiThread  android.view.ContextThemeWrapper  saveCompletedDownloadPath  android.view.ContextThemeWrapper  scheduleNextCleanup  android.view.ContextThemeWrapper  set  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  setupConsentDialogListeners  android.view.ContextThemeWrapper  sharedPreferences  android.view.ContextThemeWrapper  showSystemUI  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  
startsWith  android.view.ContextThemeWrapper  	substring  android.view.ContextThemeWrapper  substringBefore  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  tvConsentTimer  android.view.ContextThemeWrapper  webAppInterface  android.view.ContextThemeWrapper  webView  android.view.ContextThemeWrapper  LayoutParams /android.view.ContextThemeWrapper.RelativeLayout  content (android.view.ContextThemeWrapper.android  ClipboardManager 0android.view.ContextThemeWrapper.android.content  json $android.view.ContextThemeWrapper.org  
JSONException )android.view.ContextThemeWrapper.org.json  
FOCUS_DOWN android.view.View  GONE android.view.View  OVER_SCROLL_NEVER android.view.View  OnClickListener android.view.View  OnScrollChangeListener android.view.View  SYSTEM_UI_FLAG_FULLSCREEN android.view.View  SYSTEM_UI_FLAG_HIDE_NAVIGATION android.view.View  SYSTEM_UI_FLAG_IMMERSIVE_STICKY android.view.View   SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN android.view.View  %SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION android.view.View  SYSTEM_UI_FLAG_LAYOUT_STABLE android.view.View  SYSTEM_UI_FLAG_LOW_PROFILE android.view.View  VISIBLE android.view.View  findViewById android.view.View  fitsSystemWindows android.view.View  id android.view.View  isFocusable android.view.View  isFocusableInTouchMode android.view.View  isHorizontalScrollBarEnabled android.view.View  isScrollbarFadingEnabled android.view.View  isVerticalScrollBarEnabled android.view.View  layoutParams android.view.View  overScrollMode android.view.View  requestFocus android.view.View  scrollBarDefaultDelayBeforeFade android.view.View  scrollBarFadeDuration android.view.View  scrollY android.view.View  setOnClickListener android.view.View  setOnScrollChangeListener android.view.View  
setPadding android.view.View  systemUiVisibility android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> (android.view.View.OnScrollChangeListener  addFlags android.view.Window  
clearFlags android.view.Window  	decorView android.view.Window  navigationBarColor android.view.Window  setDecorFitsSystemWindows android.view.Window  statusBarColor android.view.Window  FLAG_FULLSCREEN 'android.view.WindowManager.LayoutParams  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  JavascriptInterface android.webkit  
ValueCallback android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  <SAM-CONSTRUCTOR> android.webkit.ValueCallback  LOAD_DEFAULT android.webkit.WebSettings  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  	cacheMode android.webkit.WebSettings  databaseEnabled android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  addJavascriptInterface android.webkit.WebView  	canGoBack android.webkit.WebView  
clearCache android.webkit.WebView  evaluateJavascript android.webkit.WebView  goBack android.webkit.WebView  isFocusable android.webkit.WebView  isFocusableInTouchMode android.webkit.WebView  isHorizontalScrollBarEnabled android.webkit.WebView  isScrollbarFadingEnabled android.webkit.WebView  isVerticalScrollBarEnabled android.webkit.WebView  layoutParams android.webkit.WebView  loadUrl android.webkit.WebView  onScrollChanged android.webkit.WebView  overScrollMode android.webkit.WebView  requestFocus android.webkit.WebView  scrollBarDefaultDelayBeforeFade android.webkit.WebView  scrollBarFadeDuration android.webkit.WebView  setBackgroundColor android.webkit.WebView  setOnScrollChangeListener android.webkit.WebView  settings android.webkit.WebView  
visibility android.webkit.WebView  
webViewClient android.webkit.WebView  	Exception android.webkit.WebViewClient  Handler android.webkit.WebViewClient  Intent android.webkit.WebViewClient  Log android.webkit.WebViewClient  Looper android.webkit.WebViewClient  Toast android.webkit.WebViewClient  Uri android.webkit.WebViewClient  android android.webkit.WebViewClient  isFirstTouchEventAfterLoad android.webkit.WebViewClient  onPageFinished android.webkit.WebViewClient  
startActivity android.webkit.WebViewClient  Button android.widget  LinearLayout android.widget  RelativeLayout android.widget  TextView android.widget  Toast android.widget  setOnClickListener android.widget.Button  
visibility android.widget.LinearLayout  ABOVE android.widget.RelativeLayout  ALIGN_PARENT_BOTTOM android.widget.RelativeLayout  ALIGN_PARENT_TOP android.widget.RelativeLayout  BELOW android.widget.RelativeLayout  LayoutParams android.widget.RelativeLayout  addRule *android.widget.RelativeLayout.LayoutParams  
removeRule *android.widget.RelativeLayout.LayoutParams  text android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  AUTO_DELETE_DELAY_MS #androidx.activity.ComponentActivity  AdError #androidx.activity.ComponentActivity  	AdRequest #androidx.activity.ComponentActivity  AdView #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  ConcurrentHashMap #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  CountDownTimer #androidx.activity.ComponentActivity  DEFAULT_BANNER_AD_UNIT_ID #androidx.activity.ComponentActivity  DEFAULT_REWARDED_AD_UNIT_ID #androidx.activity.ComponentActivity  
Deprecated #androidx.activity.ComponentActivity  DownloadManager #androidx.activity.ComponentActivity  Environment #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  File #androidx.activity.ComponentActivity  FullScreenContentCallback #androidx.activity.ComponentActivity  Handler #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  
JSONObject #androidx.activity.ComponentActivity  JavascriptInterface #androidx.activity.ComponentActivity  LinearLayout #androidx.activity.ComponentActivity  LoadAdError #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  Looper #androidx.activity.ComponentActivity  	MobileAds #androidx.activity.ComponentActivity  NotificationChannel #androidx.activity.ComponentActivity  NotificationCompat #androidx.activity.ComponentActivity  NotificationManager #androidx.activity.ComponentActivity  NotificationManagerCompat #androidx.activity.ComponentActivity  OnUserEarnedRewardListener #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  Regex #androidx.activity.ComponentActivity  RelativeLayout #androidx.activity.ComponentActivity  
RewardedAd #androidx.activity.ComponentActivity  RewardedAdLoadCallback #androidx.activity.ComponentActivity  Runnable #androidx.activity.ComponentActivity  SharedPreferences #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Suppress #androidx.activity.ComponentActivity  SuppressLint #androidx.activity.ComponentActivity  System #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  
URLDecoder #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  
ViewCompat #androidx.activity.ComponentActivity  	ViewGroup #androidx.activity.ComponentActivity  WebAppInterface #androidx.activity.ComponentActivity  WebSettings #androidx.activity.ComponentActivity  WebView #androidx.activity.ComponentActivity  
WebViewClient #androidx.activity.ComponentActivity  WindowCompat #androidx.activity.ComponentActivity  WindowInsetsCompat #androidx.activity.ComponentActivity  WindowInsetsControllerCompat #androidx.activity.ComponentActivity  
WindowManager #androidx.activity.ComponentActivity  activeDownloads #androidx.activity.ComponentActivity  adViewBanner #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  bannerAdPosition #androidx.activity.ComponentActivity  btnAcceptRewardedAd #androidx.activity.ComponentActivity  btnDeclineRewardedAd #androidx.activity.ComponentActivity  checkDownloadStatus #androidx.activity.ComponentActivity  clearPendingDownloadInfo #androidx.activity.ComponentActivity  contains #androidx.activity.ComponentActivity  createCacheDirectories #androidx.activity.ComponentActivity  currentBannerAdUnitId #androidx.activity.ComponentActivity  currentRewardedAdUnitId #androidx.activity.ComponentActivity  downloadManager #androidx.activity.ComponentActivity  endsWith #androidx.activity.ComponentActivity  findViewById #androidx.activity.ComponentActivity  forEach #androidx.activity.ComponentActivity  getSharedPreferences #androidx.activity.ComponentActivity  getSystemService #androidx.activity.ComponentActivity  hideConsentDialog #androidx.activity.ComponentActivity  hideSystemUI #androidx.activity.ComponentActivity  installSplashScreen #androidx.activity.ComponentActivity  isBannerAdEnabled #androidx.activity.ComponentActivity  isBlank #androidx.activity.ComponentActivity  isEmpty #androidx.activity.ComponentActivity  isFirstTouchEventAfterLoad #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  isRewardedAdEnabled #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  lastIndexOf #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  loadRewardedAd #androidx.activity.ComponentActivity  mRewardedAd #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  
onBackPressed #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  onPause #androidx.activity.ComponentActivity  onResume #androidx.activity.ComponentActivity  org #androidx.activity.ComponentActivity  pendingDownloadUrl #androidx.activity.ComponentActivity  pendingModId #androidx.activity.ComponentActivity  pendingModName #androidx.activity.ComponentActivity  pollingHandler #androidx.activity.ComponentActivity  proceedWithoutRewardedAd #androidx.activity.ComponentActivity  replace #androidx.activity.ComponentActivity  rewardedAdConsentOverlay #androidx.activity.ComponentActivity  
runOnUiThread #androidx.activity.ComponentActivity  saveCompletedDownloadPath #androidx.activity.ComponentActivity  scheduleNextCleanup #androidx.activity.ComponentActivity  set #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  setupConsentDialogListeners #androidx.activity.ComponentActivity  sharedPreferences #androidx.activity.ComponentActivity  showSystemUI #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  
startsWith #androidx.activity.ComponentActivity  	substring #androidx.activity.ComponentActivity  substringBefore #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  tvConsentTimer #androidx.activity.ComponentActivity  webAppInterface #androidx.activity.ComponentActivity  webView #androidx.activity.ComponentActivity  AUTO_DELETE_DELAY_MS -androidx.activity.ComponentActivity.Companion  	AdRequest -androidx.activity.ComponentActivity.Companion  Build -androidx.activity.ComponentActivity.Companion  ConcurrentHashMap -androidx.activity.ComponentActivity.Companion  Context -androidx.activity.ComponentActivity.Companion  
ContextCompat -androidx.activity.ComponentActivity.Companion  DEFAULT_BANNER_AD_UNIT_ID -androidx.activity.ComponentActivity.Companion  DEFAULT_REWARDED_AD_UNIT_ID -androidx.activity.ComponentActivity.Companion  DownloadManager -androidx.activity.ComponentActivity.Companion  Environment -androidx.activity.ComponentActivity.Companion  File -androidx.activity.ComponentActivity.Companion  Handler -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  
JSONObject -androidx.activity.ComponentActivity.Companion  Log -androidx.activity.ComponentActivity.Companion  Looper -androidx.activity.ComponentActivity.Companion  	MobileAds -androidx.activity.ComponentActivity.Companion  NotificationChannel -androidx.activity.ComponentActivity.Companion  NotificationCompat -androidx.activity.ComponentActivity.Companion  NotificationManager -androidx.activity.ComponentActivity.Companion  NotificationManagerCompat -androidx.activity.ComponentActivity.Companion  OnUserEarnedRewardListener -androidx.activity.ComponentActivity.Companion  PackageManager -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  Regex -androidx.activity.ComponentActivity.Companion  RelativeLayout -androidx.activity.ComponentActivity.Companion  
RewardedAd -androidx.activity.ComponentActivity.Companion  Runnable -androidx.activity.ComponentActivity.Companion  System -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  
URLDecoder -androidx.activity.ComponentActivity.Companion  Uri -androidx.activity.ComponentActivity.Companion  View -androidx.activity.ComponentActivity.Companion  
ViewCompat -androidx.activity.ComponentActivity.Companion  WebAppInterface -androidx.activity.ComponentActivity.Companion  WebSettings -androidx.activity.ComponentActivity.Companion  WindowCompat -androidx.activity.ComponentActivity.Companion  WindowInsetsCompat -androidx.activity.ComponentActivity.Companion  WindowInsetsControllerCompat -androidx.activity.ComponentActivity.Companion  
WindowManager -androidx.activity.ComponentActivity.Companion  activeDownloads -androidx.activity.ComponentActivity.Companion  adViewBanner -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  bannerAdPosition -androidx.activity.ComponentActivity.Companion  btnAcceptRewardedAd -androidx.activity.ComponentActivity.Companion  btnDeclineRewardedAd -androidx.activity.ComponentActivity.Companion  checkDownloadStatus -androidx.activity.ComponentActivity.Companion  clearPendingDownloadInfo -androidx.activity.ComponentActivity.Companion  contains -androidx.activity.ComponentActivity.Companion  createCacheDirectories -androidx.activity.ComponentActivity.Companion  currentBannerAdUnitId -androidx.activity.ComponentActivity.Companion  currentRewardedAdUnitId -androidx.activity.ComponentActivity.Companion  downloadManager -androidx.activity.ComponentActivity.Companion  endsWith -androidx.activity.ComponentActivity.Companion  findViewById -androidx.activity.ComponentActivity.Companion  forEach -androidx.activity.ComponentActivity.Companion  getSharedPreferences -androidx.activity.ComponentActivity.Companion  getSystemService -androidx.activity.ComponentActivity.Companion  hideConsentDialog -androidx.activity.ComponentActivity.Companion  hideSystemUI -androidx.activity.ComponentActivity.Companion  installSplashScreen -androidx.activity.ComponentActivity.Companion  isBannerAdEnabled -androidx.activity.ComponentActivity.Companion  isBlank -androidx.activity.ComponentActivity.Companion  isEmpty -androidx.activity.ComponentActivity.Companion  isFirstTouchEventAfterLoad -androidx.activity.ComponentActivity.Companion  
isNotEmpty -androidx.activity.ComponentActivity.Companion  isRewardedAdEnabled -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  lastIndexOf -androidx.activity.ComponentActivity.Companion  launch -androidx.activity.ComponentActivity.Companion  lifecycleScope -androidx.activity.ComponentActivity.Companion  loadRewardedAd -androidx.activity.ComponentActivity.Companion  mRewardedAd -androidx.activity.ComponentActivity.Companion  
mutableListOf -androidx.activity.ComponentActivity.Companion  pendingDownloadUrl -androidx.activity.ComponentActivity.Companion  pendingModId -androidx.activity.ComponentActivity.Companion  pendingModName -androidx.activity.ComponentActivity.Companion  pollingHandler -androidx.activity.ComponentActivity.Companion  proceedWithoutRewardedAd -androidx.activity.ComponentActivity.Companion  replace -androidx.activity.ComponentActivity.Companion  rewardedAdConsentOverlay -androidx.activity.ComponentActivity.Companion  
runOnUiThread -androidx.activity.ComponentActivity.Companion  saveCompletedDownloadPath -androidx.activity.ComponentActivity.Companion  scheduleNextCleanup -androidx.activity.ComponentActivity.Companion  set -androidx.activity.ComponentActivity.Companion  setContentView -androidx.activity.ComponentActivity.Companion  setupConsentDialogListeners -androidx.activity.ComponentActivity.Companion  sharedPreferences -androidx.activity.ComponentActivity.Companion  showSystemUI -androidx.activity.ComponentActivity.Companion  
startActivity -androidx.activity.ComponentActivity.Companion  
startsWith -androidx.activity.ComponentActivity.Companion  	substring -androidx.activity.ComponentActivity.Companion  substringBefore -androidx.activity.ComponentActivity.Companion  trim -androidx.activity.ComponentActivity.Companion  tvConsentTimer -androidx.activity.ComponentActivity.Companion  webAppInterface -androidx.activity.ComponentActivity.Companion  webView -androidx.activity.ComponentActivity.Companion  LayoutParams 2androidx.activity.ComponentActivity.RelativeLayout  content +androidx.activity.ComponentActivity.android  ClipboardManager 3androidx.activity.ComponentActivity.android.content  json 'androidx.activity.ComponentActivity.org  
JSONException ,androidx.activity.ComponentActivity.org.json  isSystemInDarkTheme androidx.compose.foundation  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextUnit androidx.compose.ui.unit  sp androidx.compose.ui.unit  NotificationCompat androidx.core.app  NotificationManagerCompat androidx.core.app  AUTO_DELETE_DELAY_MS #androidx.core.app.ComponentActivity  AdError #androidx.core.app.ComponentActivity  	AdRequest #androidx.core.app.ComponentActivity  AdView #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  ConcurrentHashMap #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  CountDownTimer #androidx.core.app.ComponentActivity  DEFAULT_BANNER_AD_UNIT_ID #androidx.core.app.ComponentActivity  DEFAULT_REWARDED_AD_UNIT_ID #androidx.core.app.ComponentActivity  
Deprecated #androidx.core.app.ComponentActivity  DownloadManager #androidx.core.app.ComponentActivity  Environment #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  File #androidx.core.app.ComponentActivity  FullScreenContentCallback #androidx.core.app.ComponentActivity  Handler #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  
JSONObject #androidx.core.app.ComponentActivity  JavascriptInterface #androidx.core.app.ComponentActivity  LinearLayout #androidx.core.app.ComponentActivity  LoadAdError #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  Looper #androidx.core.app.ComponentActivity  	MobileAds #androidx.core.app.ComponentActivity  NotificationChannel #androidx.core.app.ComponentActivity  NotificationCompat #androidx.core.app.ComponentActivity  NotificationManager #androidx.core.app.ComponentActivity  NotificationManagerCompat #androidx.core.app.ComponentActivity  OnUserEarnedRewardListener #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  Regex #androidx.core.app.ComponentActivity  RelativeLayout #androidx.core.app.ComponentActivity  
RewardedAd #androidx.core.app.ComponentActivity  RewardedAdLoadCallback #androidx.core.app.ComponentActivity  Runnable #androidx.core.app.ComponentActivity  SharedPreferences #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Suppress #androidx.core.app.ComponentActivity  SuppressLint #androidx.core.app.ComponentActivity  System #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  
URLDecoder #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  
ViewCompat #androidx.core.app.ComponentActivity  	ViewGroup #androidx.core.app.ComponentActivity  WebAppInterface #androidx.core.app.ComponentActivity  WebSettings #androidx.core.app.ComponentActivity  WebView #androidx.core.app.ComponentActivity  
WebViewClient #androidx.core.app.ComponentActivity  WindowCompat #androidx.core.app.ComponentActivity  WindowInsetsCompat #androidx.core.app.ComponentActivity  WindowInsetsControllerCompat #androidx.core.app.ComponentActivity  
WindowManager #androidx.core.app.ComponentActivity  activeDownloads #androidx.core.app.ComponentActivity  adViewBanner #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  bannerAdPosition #androidx.core.app.ComponentActivity  btnAcceptRewardedAd #androidx.core.app.ComponentActivity  btnDeclineRewardedAd #androidx.core.app.ComponentActivity  checkDownloadStatus #androidx.core.app.ComponentActivity  clearPendingDownloadInfo #androidx.core.app.ComponentActivity  contains #androidx.core.app.ComponentActivity  createCacheDirectories #androidx.core.app.ComponentActivity  currentBannerAdUnitId #androidx.core.app.ComponentActivity  currentRewardedAdUnitId #androidx.core.app.ComponentActivity  downloadManager #androidx.core.app.ComponentActivity  endsWith #androidx.core.app.ComponentActivity  findViewById #androidx.core.app.ComponentActivity  forEach #androidx.core.app.ComponentActivity  getSharedPreferences #androidx.core.app.ComponentActivity  getSystemService #androidx.core.app.ComponentActivity  hideConsentDialog #androidx.core.app.ComponentActivity  hideSystemUI #androidx.core.app.ComponentActivity  installSplashScreen #androidx.core.app.ComponentActivity  isBannerAdEnabled #androidx.core.app.ComponentActivity  isBlank #androidx.core.app.ComponentActivity  isEmpty #androidx.core.app.ComponentActivity  isFirstTouchEventAfterLoad #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  isRewardedAdEnabled #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  lastIndexOf #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  loadRewardedAd #androidx.core.app.ComponentActivity  mRewardedAd #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  org #androidx.core.app.ComponentActivity  pendingDownloadUrl #androidx.core.app.ComponentActivity  pendingModId #androidx.core.app.ComponentActivity  pendingModName #androidx.core.app.ComponentActivity  pollingHandler #androidx.core.app.ComponentActivity  proceedWithoutRewardedAd #androidx.core.app.ComponentActivity  replace #androidx.core.app.ComponentActivity  rewardedAdConsentOverlay #androidx.core.app.ComponentActivity  
runOnUiThread #androidx.core.app.ComponentActivity  saveCompletedDownloadPath #androidx.core.app.ComponentActivity  scheduleNextCleanup #androidx.core.app.ComponentActivity  set #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  setupConsentDialogListeners #androidx.core.app.ComponentActivity  sharedPreferences #androidx.core.app.ComponentActivity  showSystemUI #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  
startsWith #androidx.core.app.ComponentActivity  	substring #androidx.core.app.ComponentActivity  substringBefore #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  tvConsentTimer #androidx.core.app.ComponentActivity  webAppInterface #androidx.core.app.ComponentActivity  webView #androidx.core.app.ComponentActivity  LayoutParams 2androidx.core.app.ComponentActivity.RelativeLayout  content +androidx.core.app.ComponentActivity.android  ClipboardManager 3androidx.core.app.ComponentActivity.android.content  json 'androidx.core.app.ComponentActivity.org  
JSONException ,androidx.core.app.ComponentActivity.org.json  Builder $androidx.core.app.NotificationCompat  PRIORITY_DEFAULT $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  
setAutoCancel ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  from +androidx.core.app.NotificationManagerCompat  notify +androidx.core.app.NotificationManagerCompat  
ContextCompat androidx.core.content  FileProvider androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  Insets androidx.core.graphics  bottom androidx.core.graphics.Insets  left androidx.core.graphics.Insets  right androidx.core.graphics.Insets  top androidx.core.graphics.Insets  SplashScreen androidx.core.splashscreen  KeepOnScreenCondition 'androidx.core.splashscreen.SplashScreen  setKeepOnScreenCondition 'androidx.core.splashscreen.SplashScreen  installSplashScreen 1androidx.core.splashscreen.SplashScreen.Companion  <SAM-CONSTRUCTOR> =androidx.core.splashscreen.SplashScreen.KeepOnScreenCondition  OnApplyWindowInsetsListener androidx.core.view  
ViewCompat androidx.core.view  WindowCompat androidx.core.view  WindowInsetsCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  <SAM-CONSTRUCTOR> .androidx.core.view.OnApplyWindowInsetsListener  setOnApplyWindowInsetsListener androidx.core.view.ViewCompat  getInsetsController androidx.core.view.WindowCompat  setDecorFitsSystemWindows androidx.core.view.WindowCompat  CONSUMED %androidx.core.view.WindowInsetsCompat  	getInsets %androidx.core.view.WindowInsetsCompat  
displayCutout *androidx.core.view.WindowInsetsCompat.Type  navigationBars *androidx.core.view.WindowInsetsCompat.Type  
statusBars *androidx.core.view.WindowInsetsCompat.Type  
systemBars *androidx.core.view.WindowInsetsCompat.Type  %BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE /androidx.core.view.WindowInsetsControllerCompat  hide /androidx.core.view.WindowInsetsControllerCompat  show /androidx.core.view.WindowInsetsControllerCompat  systemBarsBehavior /androidx.core.view.WindowInsetsControllerCompat  LifecycleCoroutineScope androidx.lifecycle  lifecycleScope androidx.lifecycle  launch *androidx.lifecycle.LifecycleCoroutineScope  LocalBroadcastManager &androidx.localbroadcastmanager.content  getInstance <androidx.localbroadcastmanager.content.LocalBroadcastManager  
sendBroadcast <androidx.localbroadcastmanager.content.LocalBroadcastManager  AdError com.google.android.gms.ads  	AdRequest com.google.android.gms.ads  AdView com.google.android.gms.ads  FullScreenContentCallback com.google.android.gms.ads  LoadAdError com.google.android.gms.ads  	MobileAds com.google.android.gms.ads  OnUserEarnedRewardListener com.google.android.gms.ads  message "com.google.android.gms.ads.AdError  FullScreenContentCallback )com.google.android.gms.ads.AdLoadCallback  Log )com.google.android.gms.ads.AdLoadCallback  currentRewardedAdUnitId )com.google.android.gms.ads.AdLoadCallback  hideSystemUI )com.google.android.gms.ads.AdLoadCallback  mRewardedAd )com.google.android.gms.ads.AdLoadCallback  showSystemUI )com.google.android.gms.ads.AdLoadCallback  Builder $com.google.android.gms.ads.AdRequest  build ,com.google.android.gms.ads.AdRequest.Builder  id !com.google.android.gms.ads.AdView  layoutParams !com.google.android.gms.ads.AdView  loadAd !com.google.android.gms.ads.AdView  
visibility !com.google.android.gms.ads.AdView  loadAd %com.google.android.gms.ads.BaseAdView  Log 4com.google.android.gms.ads.FullScreenContentCallback  View 4com.google.android.gms.ads.FullScreenContentCallback  adViewBanner 4com.google.android.gms.ads.FullScreenContentCallback  clearPendingDownloadInfo 4com.google.android.gms.ads.FullScreenContentCallback  hideSystemUI 4com.google.android.gms.ads.FullScreenContentCallback  isBannerAdEnabled 4com.google.android.gms.ads.FullScreenContentCallback  isRewardedAdEnabled 4com.google.android.gms.ads.FullScreenContentCallback  loadRewardedAd 4com.google.android.gms.ads.FullScreenContentCallback  mRewardedAd 4com.google.android.gms.ads.FullScreenContentCallback  proceedWithoutRewardedAd 4com.google.android.gms.ads.FullScreenContentCallback  
runOnUiThread 4com.google.android.gms.ads.FullScreenContentCallback  showSystemUI 4com.google.android.gms.ads.FullScreenContentCallback  webView 4com.google.android.gms.ads.FullScreenContentCallback  message &com.google.android.gms.ads.LoadAdError  
initialize $com.google.android.gms.ads.MobileAds  InitializationStatus )com.google.android.gms.ads.initialization   OnInitializationCompleteListener )com.google.android.gms.ads.initialization  <SAM-CONSTRUCTOR> Jcom.google.android.gms.ads.initialization.OnInitializationCompleteListener  
RewardItem #com.google.android.gms.ads.rewarded  
RewardedAd #com.google.android.gms.ads.rewarded  RewardedAdLoadCallback #com.google.android.gms.ads.rewarded  amount .com.google.android.gms.ads.rewarded.RewardItem  type .com.google.android.gms.ads.rewarded.RewardItem  fullScreenContentCallback .com.google.android.gms.ads.rewarded.RewardedAd  load .com.google.android.gms.ads.rewarded.RewardedAd  show .com.google.android.gms.ads.rewarded.RewardedAd  FullScreenContentCallback :com.google.android.gms.ads.rewarded.RewardedAdLoadCallback  Log :com.google.android.gms.ads.rewarded.RewardedAdLoadCallback  currentRewardedAdUnitId :com.google.android.gms.ads.rewarded.RewardedAdLoadCallback  hideSystemUI :com.google.android.gms.ads.rewarded.RewardedAdLoadCallback  mRewardedAd :com.google.android.gms.ads.rewarded.RewardedAdLoadCallback  showSystemUI :com.google.android.gms.ads.rewarded.RewardedAdLoadCallback  Snackbar $com.google.android.material.snackbar  ACTION_DOWNLOAD_STATUS_UPDATE com.sidimohamed.modetaris  AUTO_DELETE_DELAY_MS com.sidimohamed.modetaris  AdError com.sidimohamed.modetaris  	AdRequest com.sidimohamed.modetaris  AdView com.sidimohamed.modetaris  Array com.sidimohamed.modetaris  AttributeSet com.sidimohamed.modetaris  Boolean com.sidimohamed.modetaris  BroadcastReceiver com.sidimohamed.modetaris  Build com.sidimohamed.modetaris  Bundle com.sidimohamed.modetaris  Button com.sidimohamed.modetaris  ComponentActivity com.sidimohamed.modetaris  ConcurrentHashMap com.sidimohamed.modetaris  ContentProvider com.sidimohamed.modetaris  
ContentValues com.sidimohamed.modetaris  Context com.sidimohamed.modetaris  
ContextCompat com.sidimohamed.modetaris  CountDownTimer com.sidimohamed.modetaris  Cursor com.sidimohamed.modetaris  
CustomWebView com.sidimohamed.modetaris  DEFAULT_BANNER_AD_UNIT_ID com.sidimohamed.modetaris  DEFAULT_REWARDED_AD_UNIT_ID com.sidimohamed.modetaris  
Deprecated com.sidimohamed.modetaris  DownloadManager com.sidimohamed.modetaris  DownloadReceiver com.sidimohamed.modetaris  DropDataProvider com.sidimohamed.modetaris  EXTRA_DOWNLOAD_ID com.sidimohamed.modetaris  Environment com.sidimohamed.modetaris  	Exception com.sidimohamed.modetaris  File com.sidimohamed.modetaris  FullScreenContentCallback com.sidimohamed.modetaris  Handler com.sidimohamed.modetaris  Int com.sidimohamed.modetaris  Intent com.sidimohamed.modetaris  
JSONObject com.sidimohamed.modetaris  JavascriptInterface com.sidimohamed.modetaris  LinearLayout com.sidimohamed.modetaris  LoadAdError com.sidimohamed.modetaris  LocalBroadcastManager com.sidimohamed.modetaris  Log com.sidimohamed.modetaris  Long com.sidimohamed.modetaris  Looper com.sidimohamed.modetaris  MainActivity com.sidimohamed.modetaris  	MobileAds com.sidimohamed.modetaris  NotificationChannel com.sidimohamed.modetaris  NotificationCompat com.sidimohamed.modetaris  NotificationManager com.sidimohamed.modetaris  NotificationManagerCompat com.sidimohamed.modetaris  OnUserEarnedRewardListener com.sidimohamed.modetaris  PENDING_DOWNLOADS_KEY com.sidimohamed.modetaris  
PREFS_NAME com.sidimohamed.modetaris  PackageManager com.sidimohamed.modetaris  R com.sidimohamed.modetaris  Regex com.sidimohamed.modetaris  RelativeLayout com.sidimohamed.modetaris  
RewardedAd com.sidimohamed.modetaris  RewardedAdLoadCallback com.sidimohamed.modetaris  Runnable com.sidimohamed.modetaris  ScrollListener com.sidimohamed.modetaris  SharedPreferences com.sidimohamed.modetaris  String com.sidimohamed.modetaris  Suppress com.sidimohamed.modetaris  SuppressLint com.sidimohamed.modetaris  System com.sidimohamed.modetaris  TextView com.sidimohamed.modetaris  Toast com.sidimohamed.modetaris  
URLDecoder com.sidimohamed.modetaris  Uri com.sidimohamed.modetaris  View com.sidimohamed.modetaris  
ViewCompat com.sidimohamed.modetaris  	ViewGroup com.sidimohamed.modetaris  WebAppInterface com.sidimohamed.modetaris  WebSettings com.sidimohamed.modetaris  WebView com.sidimohamed.modetaris  
WebViewClient com.sidimohamed.modetaris  WindowCompat com.sidimohamed.modetaris  WindowInsetsCompat com.sidimohamed.modetaris  WindowInsetsControllerCompat com.sidimohamed.modetaris  
WindowManager com.sidimohamed.modetaris  activeDownloads com.sidimohamed.modetaris  adViewBanner com.sidimohamed.modetaris  android com.sidimohamed.modetaris  apply com.sidimohamed.modetaris  bannerAdPosition com.sidimohamed.modetaris  btnAcceptRewardedAd com.sidimohamed.modetaris  btnDeclineRewardedAd com.sidimohamed.modetaris  checkDownloadStatus com.sidimohamed.modetaris  clearPendingDownloadInfo com.sidimohamed.modetaris  contains com.sidimohamed.modetaris  createCacheDirectories com.sidimohamed.modetaris  currentBannerAdUnitId com.sidimohamed.modetaris  currentRewardedAdUnitId com.sidimohamed.modetaris  downloadManager com.sidimohamed.modetaris  endsWith com.sidimohamed.modetaris  findViewById com.sidimohamed.modetaris  forEach com.sidimohamed.modetaris  getModIdForDownload com.sidimohamed.modetaris  getSharedPreferences com.sidimohamed.modetaris  getSystemService com.sidimohamed.modetaris  hideConsentDialog com.sidimohamed.modetaris  hideSystemUI com.sidimohamed.modetaris  isBannerAdEnabled com.sidimohamed.modetaris  isBlank com.sidimohamed.modetaris  isEmpty com.sidimohamed.modetaris  isFirstTouchEventAfterLoad com.sidimohamed.modetaris  
isNotEmpty com.sidimohamed.modetaris  isRewardedAdEnabled com.sidimohamed.modetaris  java com.sidimohamed.modetaris  lastIndexOf com.sidimohamed.modetaris  launch com.sidimohamed.modetaris  loadRewardedAd com.sidimohamed.modetaris  mRewardedAd com.sidimohamed.modetaris  
mutableListOf com.sidimohamed.modetaris  org com.sidimohamed.modetaris  pendingDownloadUrl com.sidimohamed.modetaris  pendingModId com.sidimohamed.modetaris  pendingModName com.sidimohamed.modetaris  pollingHandler com.sidimohamed.modetaris  proceedWithoutRewardedAd com.sidimohamed.modetaris  removeDownloadMapping com.sidimohamed.modetaris  replace com.sidimohamed.modetaris  rewardedAdConsentOverlay com.sidimohamed.modetaris  
runOnUiThread com.sidimohamed.modetaris  saveCompletedDownloadPath com.sidimohamed.modetaris  scheduleNextCleanup com.sidimohamed.modetaris  set com.sidimohamed.modetaris  setContentView com.sidimohamed.modetaris  setupConsentDialogListeners com.sidimohamed.modetaris  sharedPreferences com.sidimohamed.modetaris  showSystemUI com.sidimohamed.modetaris  
startActivity com.sidimohamed.modetaris  
startsWith com.sidimohamed.modetaris  	substring com.sidimohamed.modetaris  substringBefore com.sidimohamed.modetaris  trim com.sidimohamed.modetaris  tvConsentTimer com.sidimohamed.modetaris  webAppInterface com.sidimohamed.modetaris  webView com.sidimohamed.modetaris  scrollListener 'com.sidimohamed.modetaris.CustomWebView  scrollY 'com.sidimohamed.modetaris.CustomWebView  ACTION_DOWNLOAD_STATUS_UPDATE *com.sidimohamed.modetaris.DownloadReceiver  Context *com.sidimohamed.modetaris.DownloadReceiver  DOWNLOAD_ID_TO_MOD_ID_KEY *com.sidimohamed.modetaris.DownloadReceiver  DownloadManager *com.sidimohamed.modetaris.DownloadReceiver  EXTRA_DOWNLOAD_ID *com.sidimohamed.modetaris.DownloadReceiver  Intent *com.sidimohamed.modetaris.DownloadReceiver  
JSONObject *com.sidimohamed.modetaris.DownloadReceiver  LocalBroadcastManager *com.sidimohamed.modetaris.DownloadReceiver  Log *com.sidimohamed.modetaris.DownloadReceiver  Long *com.sidimohamed.modetaris.DownloadReceiver  PENDING_DOWNLOADS_KEY *com.sidimohamed.modetaris.DownloadReceiver  
PREFS_NAME *com.sidimohamed.modetaris.DownloadReceiver  String *com.sidimohamed.modetaris.DownloadReceiver  apply *com.sidimohamed.modetaris.DownloadReceiver  getModIdForDownload *com.sidimohamed.modetaris.DownloadReceiver  org *com.sidimohamed.modetaris.DownloadReceiver  removeDownloadMapping *com.sidimohamed.modetaris.DownloadReceiver  saveCompletedDownloadPath *com.sidimohamed.modetaris.DownloadReceiver  ACTION_DOWNLOAD_STATUS_UPDATE 4com.sidimohamed.modetaris.DownloadReceiver.Companion  Context 4com.sidimohamed.modetaris.DownloadReceiver.Companion  DOWNLOAD_ID_TO_MOD_ID_KEY 4com.sidimohamed.modetaris.DownloadReceiver.Companion  DownloadManager 4com.sidimohamed.modetaris.DownloadReceiver.Companion  EXTRA_DOWNLOAD_ID 4com.sidimohamed.modetaris.DownloadReceiver.Companion  Intent 4com.sidimohamed.modetaris.DownloadReceiver.Companion  
JSONObject 4com.sidimohamed.modetaris.DownloadReceiver.Companion  LocalBroadcastManager 4com.sidimohamed.modetaris.DownloadReceiver.Companion  Log 4com.sidimohamed.modetaris.DownloadReceiver.Companion  PENDING_DOWNLOADS_KEY 4com.sidimohamed.modetaris.DownloadReceiver.Companion  
PREFS_NAME 4com.sidimohamed.modetaris.DownloadReceiver.Companion  apply 4com.sidimohamed.modetaris.DownloadReceiver.Companion  getModIdForDownload 4com.sidimohamed.modetaris.DownloadReceiver.Companion  removeDownloadMapping 4com.sidimohamed.modetaris.DownloadReceiver.Companion  saveCompletedDownloadPath 4com.sidimohamed.modetaris.DownloadReceiver.Companion  json .com.sidimohamed.modetaris.DownloadReceiver.org  
JSONException 3com.sidimohamed.modetaris.DownloadReceiver.org.json  Log *com.sidimohamed.modetaris.DropDataProvider  TAG *com.sidimohamed.modetaris.DropDataProvider  AUTO_CLEANUP_INTERVAL_MS &com.sidimohamed.modetaris.MainActivity  AUTO_DELETE_DELAY_MS &com.sidimohamed.modetaris.MainActivity  AUTO_DELETE_ENABLED_KEY &com.sidimohamed.modetaris.MainActivity  AdError &com.sidimohamed.modetaris.MainActivity  	AdRequest &com.sidimohamed.modetaris.MainActivity  AdView &com.sidimohamed.modetaris.MainActivity  Boolean &com.sidimohamed.modetaris.MainActivity  Build &com.sidimohamed.modetaris.MainActivity  Bundle &com.sidimohamed.modetaris.MainActivity  Button &com.sidimohamed.modetaris.MainActivity  ConcurrentHashMap &com.sidimohamed.modetaris.MainActivity  Context &com.sidimohamed.modetaris.MainActivity  
ContextCompat &com.sidimohamed.modetaris.MainActivity  CountDownTimer &com.sidimohamed.modetaris.MainActivity  DEFAULT_BANNER_AD_UNIT_ID &com.sidimohamed.modetaris.MainActivity  DEFAULT_REWARDED_AD_UNIT_ID &com.sidimohamed.modetaris.MainActivity  
Deprecated &com.sidimohamed.modetaris.MainActivity  DownloadManager &com.sidimohamed.modetaris.MainActivity  Environment &com.sidimohamed.modetaris.MainActivity  	Exception &com.sidimohamed.modetaris.MainActivity  File &com.sidimohamed.modetaris.MainActivity  FullScreenContentCallback &com.sidimohamed.modetaris.MainActivity  Handler &com.sidimohamed.modetaris.MainActivity  Int &com.sidimohamed.modetaris.MainActivity  Intent &com.sidimohamed.modetaris.MainActivity  
JSONObject &com.sidimohamed.modetaris.MainActivity  JavascriptInterface &com.sidimohamed.modetaris.MainActivity  LinearLayout &com.sidimohamed.modetaris.MainActivity  LoadAdError &com.sidimohamed.modetaris.MainActivity  Log &com.sidimohamed.modetaris.MainActivity  Long &com.sidimohamed.modetaris.MainActivity  Looper &com.sidimohamed.modetaris.MainActivity  MOD_DOWNLOAD_TIMESTAMPS_KEY &com.sidimohamed.modetaris.MainActivity  	MobileAds &com.sidimohamed.modetaris.MainActivity  NotificationChannel &com.sidimohamed.modetaris.MainActivity  NotificationCompat &com.sidimohamed.modetaris.MainActivity  NotificationManager &com.sidimohamed.modetaris.MainActivity  NotificationManagerCompat &com.sidimohamed.modetaris.MainActivity  OnUserEarnedRewardListener &com.sidimohamed.modetaris.MainActivity  PENDING_DOWNLOADS_KEY &com.sidimohamed.modetaris.MainActivity  
PREFS_NAME &com.sidimohamed.modetaris.MainActivity  PackageManager &com.sidimohamed.modetaris.MainActivity  R &com.sidimohamed.modetaris.MainActivity  Regex &com.sidimohamed.modetaris.MainActivity  RelativeLayout &com.sidimohamed.modetaris.MainActivity  
RewardedAd &com.sidimohamed.modetaris.MainActivity  RewardedAdLoadCallback &com.sidimohamed.modetaris.MainActivity  Runnable &com.sidimohamed.modetaris.MainActivity  SharedPreferences &com.sidimohamed.modetaris.MainActivity  String &com.sidimohamed.modetaris.MainActivity  Suppress &com.sidimohamed.modetaris.MainActivity  SuppressLint &com.sidimohamed.modetaris.MainActivity  System &com.sidimohamed.modetaris.MainActivity  TextView &com.sidimohamed.modetaris.MainActivity  Toast &com.sidimohamed.modetaris.MainActivity  
URLDecoder &com.sidimohamed.modetaris.MainActivity  Uri &com.sidimohamed.modetaris.MainActivity  View &com.sidimohamed.modetaris.MainActivity  
ViewCompat &com.sidimohamed.modetaris.MainActivity  	ViewGroup &com.sidimohamed.modetaris.MainActivity  WebAppInterface &com.sidimohamed.modetaris.MainActivity  WebSettings &com.sidimohamed.modetaris.MainActivity  WebView &com.sidimohamed.modetaris.MainActivity  
WebViewClient &com.sidimohamed.modetaris.MainActivity  WindowCompat &com.sidimohamed.modetaris.MainActivity  WindowInsetsCompat &com.sidimohamed.modetaris.MainActivity  WindowInsetsControllerCompat &com.sidimohamed.modetaris.MainActivity  
WindowManager &com.sidimohamed.modetaris.MainActivity  activeDownloads &com.sidimohamed.modetaris.MainActivity  adViewBanner &com.sidimohamed.modetaris.MainActivity  android &com.sidimohamed.modetaris.MainActivity  bannerAdPosition &com.sidimohamed.modetaris.MainActivity  btnAcceptRewardedAd &com.sidimohamed.modetaris.MainActivity  btnDeclineRewardedAd &com.sidimohamed.modetaris.MainActivity  cacheDir &com.sidimohamed.modetaris.MainActivity  checkDownloadStatus &com.sidimohamed.modetaris.MainActivity  cleanupHandler &com.sidimohamed.modetaris.MainActivity  cleanupRunnable &com.sidimohamed.modetaris.MainActivity  clearPendingDownloadInfo &com.sidimohamed.modetaris.MainActivity  closeAllModals &com.sidimohamed.modetaris.MainActivity  consentTimer &com.sidimohamed.modetaris.MainActivity  contains &com.sidimohamed.modetaris.MainActivity  createCacheDirectories &com.sidimohamed.modetaris.MainActivity  currentBannerAdUnitId &com.sidimohamed.modetaris.MainActivity  currentRewardedAdUnitId &com.sidimohamed.modetaris.MainActivity  downloadManager &com.sidimohamed.modetaris.MainActivity  endsWith &com.sidimohamed.modetaris.MainActivity  findViewById &com.sidimohamed.modetaris.MainActivity  forEach &com.sidimohamed.modetaris.MainActivity  getExternalFilesDir &com.sidimohamed.modetaris.MainActivity  getSharedPreferences &com.sidimohamed.modetaris.MainActivity  getSystemService &com.sidimohamed.modetaris.MainActivity  hideConsentDialog &com.sidimohamed.modetaris.MainActivity  hideSystemUI &com.sidimohamed.modetaris.MainActivity  initiateActualDownload &com.sidimohamed.modetaris.MainActivity  installSplashScreen &com.sidimohamed.modetaris.MainActivity  isBannerAdEnabled &com.sidimohamed.modetaris.MainActivity  isBlank &com.sidimohamed.modetaris.MainActivity  isEmpty &com.sidimohamed.modetaris.MainActivity  isFirstTouchEventAfterLoad &com.sidimohamed.modetaris.MainActivity  
isNotEmpty &com.sidimohamed.modetaris.MainActivity  isRewardedAdEnabled &com.sidimohamed.modetaris.MainActivity  java &com.sidimohamed.modetaris.MainActivity  lastIndexOf &com.sidimohamed.modetaris.MainActivity  launch &com.sidimohamed.modetaris.MainActivity  lifecycleScope &com.sidimohamed.modetaris.MainActivity  loadRewardedAd &com.sidimohamed.modetaris.MainActivity  mRewardedAd &com.sidimohamed.modetaris.MainActivity  
mutableListOf &com.sidimohamed.modetaris.MainActivity  org &com.sidimohamed.modetaris.MainActivity  pendingDownloadUrl &com.sidimohamed.modetaris.MainActivity  pendingModId &com.sidimohamed.modetaris.MainActivity  pendingModName &com.sidimohamed.modetaris.MainActivity  performCleanupOfOldDownloads &com.sidimohamed.modetaris.MainActivity  pollingHandler &com.sidimohamed.modetaris.MainActivity  proceedWithRewardedAd &com.sidimohamed.modetaris.MainActivity  proceedWithoutRewardedAd &com.sidimohamed.modetaris.MainActivity  replace &com.sidimohamed.modetaris.MainActivity  	resources &com.sidimohamed.modetaris.MainActivity  rewardedAdConsentOverlay &com.sidimohamed.modetaris.MainActivity  
runOnUiThread &com.sidimohamed.modetaris.MainActivity  saveCompletedDownloadPath &com.sidimohamed.modetaris.MainActivity  scheduleNextCleanup &com.sidimohamed.modetaris.MainActivity  set &com.sidimohamed.modetaris.MainActivity  setContentView &com.sidimohamed.modetaris.MainActivity  setupConsentDialogListeners &com.sidimohamed.modetaris.MainActivity  sharedPreferences &com.sidimohamed.modetaris.MainActivity  showAutoDeleteNotification &com.sidimohamed.modetaris.MainActivity  showConsentDialog &com.sidimohamed.modetaris.MainActivity  showRewardedAdInternal &com.sidimohamed.modetaris.MainActivity  showSystemUI &com.sidimohamed.modetaris.MainActivity  
startActivity &com.sidimohamed.modetaris.MainActivity  
startsWith &com.sidimohamed.modetaris.MainActivity  	substring &com.sidimohamed.modetaris.MainActivity  substringBefore &com.sidimohamed.modetaris.MainActivity  trim &com.sidimohamed.modetaris.MainActivity  tvConsentTimer &com.sidimohamed.modetaris.MainActivity  webAppInterface &com.sidimohamed.modetaris.MainActivity  webView &com.sidimohamed.modetaris.MainActivity  window &com.sidimohamed.modetaris.MainActivity  AUTO_DELETE_DELAY_MS 0com.sidimohamed.modetaris.MainActivity.Companion  	AdRequest 0com.sidimohamed.modetaris.MainActivity.Companion  Build 0com.sidimohamed.modetaris.MainActivity.Companion  ConcurrentHashMap 0com.sidimohamed.modetaris.MainActivity.Companion  Context 0com.sidimohamed.modetaris.MainActivity.Companion  
ContextCompat 0com.sidimohamed.modetaris.MainActivity.Companion  DEFAULT_BANNER_AD_UNIT_ID 0com.sidimohamed.modetaris.MainActivity.Companion  DEFAULT_REWARDED_AD_UNIT_ID 0com.sidimohamed.modetaris.MainActivity.Companion  DownloadManager 0com.sidimohamed.modetaris.MainActivity.Companion  Environment 0com.sidimohamed.modetaris.MainActivity.Companion  File 0com.sidimohamed.modetaris.MainActivity.Companion  Handler 0com.sidimohamed.modetaris.MainActivity.Companion  Intent 0com.sidimohamed.modetaris.MainActivity.Companion  
JSONObject 0com.sidimohamed.modetaris.MainActivity.Companion  Log 0com.sidimohamed.modetaris.MainActivity.Companion  Looper 0com.sidimohamed.modetaris.MainActivity.Companion  	MobileAds 0com.sidimohamed.modetaris.MainActivity.Companion  NotificationChannel 0com.sidimohamed.modetaris.MainActivity.Companion  NotificationCompat 0com.sidimohamed.modetaris.MainActivity.Companion  NotificationManager 0com.sidimohamed.modetaris.MainActivity.Companion  NotificationManagerCompat 0com.sidimohamed.modetaris.MainActivity.Companion  OnUserEarnedRewardListener 0com.sidimohamed.modetaris.MainActivity.Companion  PackageManager 0com.sidimohamed.modetaris.MainActivity.Companion  R 0com.sidimohamed.modetaris.MainActivity.Companion  Regex 0com.sidimohamed.modetaris.MainActivity.Companion  RelativeLayout 0com.sidimohamed.modetaris.MainActivity.Companion  
RewardedAd 0com.sidimohamed.modetaris.MainActivity.Companion  Runnable 0com.sidimohamed.modetaris.MainActivity.Companion  System 0com.sidimohamed.modetaris.MainActivity.Companion  Toast 0com.sidimohamed.modetaris.MainActivity.Companion  
URLDecoder 0com.sidimohamed.modetaris.MainActivity.Companion  Uri 0com.sidimohamed.modetaris.MainActivity.Companion  View 0com.sidimohamed.modetaris.MainActivity.Companion  
ViewCompat 0com.sidimohamed.modetaris.MainActivity.Companion  WebAppInterface 0com.sidimohamed.modetaris.MainActivity.Companion  WebSettings 0com.sidimohamed.modetaris.MainActivity.Companion  WindowCompat 0com.sidimohamed.modetaris.MainActivity.Companion  WindowInsetsCompat 0com.sidimohamed.modetaris.MainActivity.Companion  WindowInsetsControllerCompat 0com.sidimohamed.modetaris.MainActivity.Companion  
WindowManager 0com.sidimohamed.modetaris.MainActivity.Companion  activeDownloads 0com.sidimohamed.modetaris.MainActivity.Companion  adViewBanner 0com.sidimohamed.modetaris.MainActivity.Companion  android 0com.sidimohamed.modetaris.MainActivity.Companion  bannerAdPosition 0com.sidimohamed.modetaris.MainActivity.Companion  btnAcceptRewardedAd 0com.sidimohamed.modetaris.MainActivity.Companion  btnDeclineRewardedAd 0com.sidimohamed.modetaris.MainActivity.Companion  checkDownloadStatus 0com.sidimohamed.modetaris.MainActivity.Companion  clearPendingDownloadInfo 0com.sidimohamed.modetaris.MainActivity.Companion  contains 0com.sidimohamed.modetaris.MainActivity.Companion  createCacheDirectories 0com.sidimohamed.modetaris.MainActivity.Companion  currentBannerAdUnitId 0com.sidimohamed.modetaris.MainActivity.Companion  currentRewardedAdUnitId 0com.sidimohamed.modetaris.MainActivity.Companion  downloadManager 0com.sidimohamed.modetaris.MainActivity.Companion  endsWith 0com.sidimohamed.modetaris.MainActivity.Companion  findViewById 0com.sidimohamed.modetaris.MainActivity.Companion  forEach 0com.sidimohamed.modetaris.MainActivity.Companion  getSharedPreferences 0com.sidimohamed.modetaris.MainActivity.Companion  getSystemService 0com.sidimohamed.modetaris.MainActivity.Companion  hideConsentDialog 0com.sidimohamed.modetaris.MainActivity.Companion  hideSystemUI 0com.sidimohamed.modetaris.MainActivity.Companion  installSplashScreen 0com.sidimohamed.modetaris.MainActivity.Companion  isBannerAdEnabled 0com.sidimohamed.modetaris.MainActivity.Companion  isBlank 0com.sidimohamed.modetaris.MainActivity.Companion  isEmpty 0com.sidimohamed.modetaris.MainActivity.Companion  isFirstTouchEventAfterLoad 0com.sidimohamed.modetaris.MainActivity.Companion  
isNotEmpty 0com.sidimohamed.modetaris.MainActivity.Companion  isRewardedAdEnabled 0com.sidimohamed.modetaris.MainActivity.Companion  java 0com.sidimohamed.modetaris.MainActivity.Companion  lastIndexOf 0com.sidimohamed.modetaris.MainActivity.Companion  launch 0com.sidimohamed.modetaris.MainActivity.Companion  lifecycleScope 0com.sidimohamed.modetaris.MainActivity.Companion  loadRewardedAd 0com.sidimohamed.modetaris.MainActivity.Companion  mRewardedAd 0com.sidimohamed.modetaris.MainActivity.Companion  
mutableListOf 0com.sidimohamed.modetaris.MainActivity.Companion  pendingDownloadUrl 0com.sidimohamed.modetaris.MainActivity.Companion  pendingModId 0com.sidimohamed.modetaris.MainActivity.Companion  pendingModName 0com.sidimohamed.modetaris.MainActivity.Companion  pollingHandler 0com.sidimohamed.modetaris.MainActivity.Companion  proceedWithoutRewardedAd 0com.sidimohamed.modetaris.MainActivity.Companion  replace 0com.sidimohamed.modetaris.MainActivity.Companion  rewardedAdConsentOverlay 0com.sidimohamed.modetaris.MainActivity.Companion  
runOnUiThread 0com.sidimohamed.modetaris.MainActivity.Companion  saveCompletedDownloadPath 0com.sidimohamed.modetaris.MainActivity.Companion  scheduleNextCleanup 0com.sidimohamed.modetaris.MainActivity.Companion  set 0com.sidimohamed.modetaris.MainActivity.Companion  setContentView 0com.sidimohamed.modetaris.MainActivity.Companion  setupConsentDialogListeners 0com.sidimohamed.modetaris.MainActivity.Companion  sharedPreferences 0com.sidimohamed.modetaris.MainActivity.Companion  showSystemUI 0com.sidimohamed.modetaris.MainActivity.Companion  
startActivity 0com.sidimohamed.modetaris.MainActivity.Companion  
startsWith 0com.sidimohamed.modetaris.MainActivity.Companion  	substring 0com.sidimohamed.modetaris.MainActivity.Companion  substringBefore 0com.sidimohamed.modetaris.MainActivity.Companion  trim 0com.sidimohamed.modetaris.MainActivity.Companion  tvConsentTimer 0com.sidimohamed.modetaris.MainActivity.Companion  webAppInterface 0com.sidimohamed.modetaris.MainActivity.Companion  webView 0com.sidimohamed.modetaris.MainActivity.Companion  LayoutParams 5com.sidimohamed.modetaris.MainActivity.RelativeLayout  Context 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  DownloadManager 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  Log 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  Regex 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  Toast 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  
URLDecoder 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  activeDownloads 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  android 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  checkDownloadStatus 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  context 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  downloadManager 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  endsWith 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  generateFilenameFromUrl 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  isEmpty 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  
isNotEmpty 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  isRewardedAdEnabled 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  lastIndexOf 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  loadRewardedAd 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  pendingDownloadUrl 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  pendingModId 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  pendingModName 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  pollingHandler 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  replace 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  
runOnUiThread 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  saveCompletedDownloadPath 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  scheduleDownloadCheck 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  showAdOrDownload 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  	substring 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  substringBefore 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  trim 6com.sidimohamed.modetaris.MainActivity.WebAppInterface  content .com.sidimohamed.modetaris.MainActivity.android  ClipboardManager 6com.sidimohamed.modetaris.MainActivity.android.content  json *com.sidimohamed.modetaris.MainActivity.org  
JSONException /com.sidimohamed.modetaris.MainActivity.org.json  adViewBanner com.sidimohamed.modetaris.R.id  btnAcceptRewardedAd com.sidimohamed.modetaris.R.id  btnDeclineRewardedAd com.sidimohamed.modetaris.R.id  rewardedAdConsentOverlay com.sidimohamed.modetaris.R.id  tvConsentTimer com.sidimohamed.modetaris.R.id  webViewMain com.sidimohamed.modetaris.R.id  
activity_main "com.sidimohamed.modetaris.R.layout  LayoutParams (com.sidimohamed.modetaris.RelativeLayout  onScrollTopChanged (com.sidimohamed.modetaris.ScrollListener  content !com.sidimohamed.modetaris.android  ClipboardManager )com.sidimohamed.modetaris.android.content  json com.sidimohamed.modetaris.org  
JSONException "com.sidimohamed.modetaris.org.json  Boolean "com.sidimohamed.modetaris.ui.theme  Build "com.sidimohamed.modetaris.ui.theme  
Composable "com.sidimohamed.modetaris.ui.theme  DarkColorScheme "com.sidimohamed.modetaris.ui.theme  
FontFamily "com.sidimohamed.modetaris.ui.theme  
FontWeight "com.sidimohamed.modetaris.ui.theme  LightColorScheme "com.sidimohamed.modetaris.ui.theme  ModetarisTheme "com.sidimohamed.modetaris.ui.theme  Pink40 "com.sidimohamed.modetaris.ui.theme  Pink80 "com.sidimohamed.modetaris.ui.theme  Purple40 "com.sidimohamed.modetaris.ui.theme  Purple80 "com.sidimohamed.modetaris.ui.theme  PurpleGrey40 "com.sidimohamed.modetaris.ui.theme  PurpleGrey80 "com.sidimohamed.modetaris.ui.theme  
Typography "com.sidimohamed.modetaris.ui.theme  Unit "com.sidimohamed.modetaris.ui.theme  File java.io  
FileFilter java.io  FilenameFilter java.io  delete java.io.File  exists java.io.File  	listFiles java.io.File  mkdirs java.io.File  name java.io.File  setReadable java.io.File  setWritable java.io.File  <SAM-CONSTRUCTOR> java.io.FileFilter  <SAM-CONSTRUCTOR> java.io.FilenameFilter  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  
URLDecoder java.net  decode java.net.URLDecoder  HashSet 	java.util  ConcurrentHashMap java.util.concurrent  remove &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  Array kotlin  CharSequence kotlin  
Deprecated kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function5 kotlin  Nothing kotlin  Suppress kotlin  apply kotlin  not kotlin.Boolean  isEmpty kotlin.CharSequence  sp 
kotlin.Double  	compareTo 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  toString kotlin.Long  
unaryMinus kotlin.Long  endsWith 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  lastIndexOf 
kotlin.String  length 
kotlin.String  plus 
kotlin.String  replace 
kotlin.String  
startsWith 
kotlin.String  	substring 
kotlin.String  trim 
kotlin.String  MutableIterator kotlin.collections  MutableList kotlin.collections  contains kotlin.collections  forEach kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  lastIndexOf kotlin.collections  
mutableListOf kotlin.collections  set kotlin.collections  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  iterator kotlin.collections.MutableList  size kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  endsWith 	kotlin.io  
startsWith 	kotlin.io  java 
kotlin.jvm  contains 
kotlin.ranges  java kotlin.reflect.KClass  contains kotlin.sequences  forEach kotlin.sequences  lastIndexOf kotlin.sequences  Regex kotlin.text  contains kotlin.text  endsWith kotlin.text  forEach kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  lastIndexOf kotlin.text  replace kotlin.text  set kotlin.text  
startsWith kotlin.text  	substring kotlin.text  substringBefore kotlin.text  trim kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  AUTO_DELETE_DELAY_MS !kotlinx.coroutines.CoroutineScope  	AdRequest !kotlinx.coroutines.CoroutineScope  Context !kotlinx.coroutines.CoroutineScope  DEFAULT_BANNER_AD_UNIT_ID !kotlinx.coroutines.CoroutineScope  DEFAULT_REWARDED_AD_UNIT_ID !kotlinx.coroutines.CoroutineScope  Handler !kotlinx.coroutines.CoroutineScope  Intent !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  Looper !kotlinx.coroutines.CoroutineScope  	MobileAds !kotlinx.coroutines.CoroutineScope  R !kotlinx.coroutines.CoroutineScope  RelativeLayout !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  Uri !kotlinx.coroutines.CoroutineScope  View !kotlinx.coroutines.CoroutineScope  WebAppInterface !kotlinx.coroutines.CoroutineScope  WebSettings !kotlinx.coroutines.CoroutineScope  adViewBanner !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  bannerAdPosition !kotlinx.coroutines.CoroutineScope  btnAcceptRewardedAd !kotlinx.coroutines.CoroutineScope  btnDeclineRewardedAd !kotlinx.coroutines.CoroutineScope  createCacheDirectories !kotlinx.coroutines.CoroutineScope  currentBannerAdUnitId !kotlinx.coroutines.CoroutineScope  currentRewardedAdUnitId !kotlinx.coroutines.CoroutineScope  downloadManager !kotlinx.coroutines.CoroutineScope  findViewById !kotlinx.coroutines.CoroutineScope  getSharedPreferences !kotlinx.coroutines.CoroutineScope  getSystemService !kotlinx.coroutines.CoroutineScope  hideSystemUI !kotlinx.coroutines.CoroutineScope  isBannerAdEnabled !kotlinx.coroutines.CoroutineScope  isFirstTouchEventAfterLoad !kotlinx.coroutines.CoroutineScope  isRewardedAdEnabled !kotlinx.coroutines.CoroutineScope  loadRewardedAd !kotlinx.coroutines.CoroutineScope  mRewardedAd !kotlinx.coroutines.CoroutineScope  rewardedAdConsentOverlay !kotlinx.coroutines.CoroutineScope  
runOnUiThread !kotlinx.coroutines.CoroutineScope  scheduleNextCleanup !kotlinx.coroutines.CoroutineScope  setContentView !kotlinx.coroutines.CoroutineScope  setupConsentDialogListeners !kotlinx.coroutines.CoroutineScope  sharedPreferences !kotlinx.coroutines.CoroutineScope  
startActivity !kotlinx.coroutines.CoroutineScope  tvConsentTimer !kotlinx.coroutines.CoroutineScope  webAppInterface !kotlinx.coroutines.CoroutineScope  webView !kotlinx.coroutines.CoroutineScope  
JSONException org.json  
JSONObject org.json  getLong org.json.JSONObject  has org.json.JSONObject  keys org.json.JSONObject  	optString org.json.JSONObject  put org.json.JSONObject  remove org.json.JSONObject  toString org.json.JSONObject                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           