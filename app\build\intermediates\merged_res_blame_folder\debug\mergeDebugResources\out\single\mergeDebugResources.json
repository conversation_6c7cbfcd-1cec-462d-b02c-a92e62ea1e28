[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-mdpi_ic_launcher_new.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-mdpi\\ic_launcher_new.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\drawable_splash_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\drawable\\splash_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\drawable_ic_launcher_foreground.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\drawable\\ic_launcher_foreground.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-xxhdpi_ic_launcher_new.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-xxhdpi\\ic_launcher_new.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-xhdpi_ic_launcher_new.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-xhdpi\\ic_launcher_new.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-xxxhdpi_ic_launcher_foreground.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-xxxhdpi\\ic_launcher_foreground.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-hdpi_ic_launcher_new.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-hdpi\\ic_launcher_new.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\xml\\backup_rules.xml"}, {"merged": "com.sidimohamed.modetaris.app-debug-73:/layout_activity_main.xml.flat", "source": "com.sidimohamed.modetaris.app-main-75:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\drawable_loading_photo.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\drawable\\loading_photo.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\xml_provider_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\xml\\provider_paths.xml"}, {"merged": "com.sidimohamed.modetaris.app-debug-73:/drawable_button_decline_background.xml.flat", "source": "com.sidimohamed.modetaris.app-main-75:/drawable/button_decline_background.xml"}, {"merged": "com.sidimohamed.modetaris.app-debug-73:/drawable_button_accept_background.xml.flat", "source": "com.sidimohamed.modetaris.app-main-75:/drawable/button_accept_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-debug-73:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.sidimohamed.modetaris.app-main-75:\\drawable\\ic_notification.xml"}, {"merged": "com.sidimohamed.modetaris.app-debug-73:/drawable_dialog_background.xml.flat", "source": "com.sidimohamed.modetaris.app-main-75:/drawable/dialog_background.xml"}]